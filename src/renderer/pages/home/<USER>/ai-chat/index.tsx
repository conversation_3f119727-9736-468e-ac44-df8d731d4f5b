
import React, { useEffect, useRef, CSSProperties, useState, useMemo, useCallback, forwardRef, useImperativeHandle, useContext } from 'react';
import type { BubbleProps } from '@ant-design/x';
import { Bubble } from '@ant-design/x';
import { Typography } from 'antd';
import markdownit from 'markdown-it';
import hljs from 'highlight.js';
import mermaid from 'mermaid'; // 导入 mermaid
import '../../../../styles/ai-chat/ai-chat.css'; // 导入AI聊天相关样式
import { IChatRequestMessage, IChatResponseMessage, IChatContext, IChat } from '../../../../types/llm';
import { aiService } from '../../../../services/api/ai-service'; // Assuming aiService provides access
import NextCharService from '../../../../llm/services/NextChatService'; // Import base class for type
import useChatStore from '../../../../stores/chatStore'; // 修正大小写
import { FilePathContext } from '../../index'; // 导入文件路径上下文
import useProviderStore from '../../../../stores/providerStore'; // Import the provider store
import { createChatService } from '../../../../llm/services/ChatServiceFactory'; // Import the factory
import {
  DEFAULT_MAX_TOKENS,
  DEFAULT_PROVIDER,
  DEFAULT_TEMPERATURE,
} from '../../../../utils/consts';
import {
  Brain,
  ChevronUp,
  ChevronDown,
  Volume2,
} from 'lucide-react';
import AudioPlayer from '../../../../components/audio-player';
import { useTranslation } from 'react-i18next';
import useToolStore from '../../../../stores/toolStore';
import { useSystemStore } from '../../../../stores/systemStore';
import { useVoiceManager, mergeAudioBuffers } from '../../../../components/voice-manager';

// 添加Loading动画组件
const LoadingDots = () => {
  return (
    <div className="loading-dots">
      <span></span>
      <span></span>
      <span></span>
    </div>
  );
};

// 定义组件ref类型
export interface AiChatRef {
  startConversation: (q?: string) => void;
  reset: () => Promise<void>;
}

interface AiChatProps {
  query: string;
  onComplete: () => void;
}

// 添加一个类用于标记已处理的代码块
const PROCESSED_CLASS = 'code-block-processed';
// 添加一个类用于标记已处理的Mermaid块
const MERMAID_PROCESSED_CLASS = 'mermaid-processed';

// 验证是否为有效的文件路径或目录路径
const isValidPath = (path: string): { isValid: boolean; isDirectory: boolean } => {
  // 基本长度检查
  if (path.length < 2) return { isValid: false, isDirectory: false };
  
  // 不能是URL
  if (/^[a-zA-Z]+:\/\//.test(path)) return { isValid: false, isDirectory: false };
  
  // 不能包含特殊字符（HTML、Markdown等）
  if (/<[^>]+>/.test(path) || /\*\*|\*|__|\[|\]/.test(path)) return { isValid: false, isDirectory: false };
  
  // 必须是绝对路径或用户目录路径
  const isAbsolute = /^[A-Za-z]:\\/.test(path) || // Windows C:\
                     /^\\\\/.test(path) ||        // UNC \\server\
                     /^\//.test(path) ||          // Unix /
                     /^~\//.test(path);           // User ~/
  
  if (!isAbsolute) return { isValid: false, isDirectory: false };
  
  // 移除末尾的斜杠进行判断
  const normalizedPath = path.replace(/[/\\]+$/, '');
  
  // 检查是否有文件扩展名
  const hasExtension = /\.[a-zA-Z0-9]+$/.test(normalizedPath);
  
  if (hasExtension) {
    // 有扩展名，当作文件处理
    const commonExtensions = [
      'txt', 'md', 'doc', 'docx', 'pdf', 'rtf',
      'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp',
      'mp3', 'wav', 'mp4', 'avi', 'mov', 'mkv',
      'zip', 'rar', '7z', 'tar', 'gz',
      'js', 'ts', 'tsx', 'jsx', 'html', 'css', 'scss', 'less',
      'py', 'java', 'cpp', 'c', 'h', 'cs', 'php', 'rb', 'go',
      'json', 'xml', 'yaml', 'yml', 'ini', 'conf', 'log',
      'exe', 'app', 'dmg', 'deb', 'rpm',
      'xls', 'xlsx', 'ppt', 'pptx', 'csv'
    ];
    
    const extension = normalizedPath.split('.').pop()?.toLowerCase();
    const isValidExtension = extension ? commonExtensions.includes(extension) : false;
    return { isValid: isValidExtension, isDirectory: false };
  } else {
    // 没有扩展名，可能是目录路径
    // 检查是否像目录路径（不以点开头的最后一段，且长度合理）
    const segments = normalizedPath.split(/[/\\]/);
    const lastSegment = segments[segments.length - 1];
    
    // 排除一些明显不是目录的情况
    if (!lastSegment || lastSegment.length === 0) return { isValid: false, isDirectory: false };
    if (lastSegment.startsWith('.') && lastSegment.length < 10) return { isValid: false, isDirectory: false }; // 隐藏文件
    if (/^[0-9]+$/.test(lastSegment)) return { isValid: false, isDirectory: false }; // 纯数字
    
    // 常见的目录名模式
    const isLikelyDirectory = segments.length >= 2 && // 至少两级路径
                             lastSegment.length >= 1 && // 最后一段有内容
                             !/\s{2,}/.test(normalizedPath); // 不包含多个连续空格
    
    return { isValid: isLikelyDirectory, isDirectory: true };
  }
};

// 修改markdown-it配置，为每个代码块添加特殊标记
const md: markdownit = markdownit({
  html: true,
  breaks: true,
  highlight: function (str: string, lang: string): string {
    // 如果语言是 mermaid，则特殊处理
    if (lang === 'mermaid') {
      // 返回包含 mermaid 代码的 pre 标签，添加特定类名
      return `<pre class="language-mermaid"><code>${md.utils.escapeHtml(str)}</code></pre>`;
    }
    if (lang && hljs.getLanguage(lang)) {
      try {
        const code = hljs.highlight(str, { language: lang, ignoreIllegals: true }).value;
        // 保留 hljs 类用于样式，添加 PROCESSED_CLASS 的标记目标类
        return `<pre class="hljs code-block" data-lang="${lang}"><code>${code}</code></pre>`;
      } catch (__) {
        // 捕获错误但不处理
      }
    }
    // 使用通用的高亮处理，添加 code-block 类
    return `<pre class="hljs code-block"><code>${md.utils.escapeHtml(str)}</code></pre>`;
  }
});


// 添加文件路径处理的后处理函数 
const processFilePathsInHtml = (html: string): string => {
  let result = html;

  try {
    // 首先标记所有代码块（pre标签），避免处理其中的内容
    const codeBlockRegex = /<pre[^>]*>[\s\S]*?<\/pre>/g;
    const codeBlocks: string[] = [];
    
    // 保存代码块内容并用占位符替换
    result = result.replace(codeBlockRegex, (match) => {
      const index = codeBlocks.length;
      codeBlocks.push(match);
      return `__CODE_BLOCK_${index}__`;
    });

    // 检测所有可能的文件路径模式（现在只在非代码块区域）
    const pathPatterns = [
      // 被独立code标签包围的路径: <code>/path/to/file.ext</code> 或 <code>/path/to/directory</code>
      /<code>([^<>]*?[/\\][^<>]*?(?:\.[a-zA-Z0-9]+|[^<>]*?))<\/code>/g,
      // 直接出现的Unix路径（文件）: /path/to/file.ext
      /\/(?:[a-zA-Z0-9._-]+\/)+[a-zA-Z0-9._-]+\.[a-zA-Z0-9]+/g,
      // 直接出现的Unix路径（目录）: /path/to/directory 或 /path/to/directory/
      /\/[a-zA-Z0-9._-]+(?:\/[a-zA-Z0-9._-]+)*\/?(?=\s|下|中|，|。|！|？|；|：|<|文|件|夹|目|录|路|径|的|了|操|作|完|成|保|存|到|在|里|内|上|后|前|左|右|等|和|与|或|及|并|还|也|就|都|而|且|之|从|时|已|被|给|于|过|有|很|将|会|可|能|所|多|大|小|新|旧|好|坏|高|低|长|短|快|慢|重|轻|深|浅|冷|热|用|为|是|不|再|更|最|太|非|常|只|但|却|然|否|则|即|若|如|$)/g,
      // 直接出现的Windows路径（文件）: C:\path\to\file.ext
      /[A-Za-z]:\\(?:[a-zA-Z0-9._\-\s]+\\)+[a-zA-Z0-9._\-\s]+\.[a-zA-Z0-9]+/g,
      // 直接出现的Windows路径（目录）: C:\path\to\directory 或 C:\path\to\directory\
      /[A-Za-z]:\\[a-zA-Z0-9._\-\s]+(?:\\[a-zA-Z0-9._\-\s]+)*\\?(?=\s|下|中|，|。|！|？|；|：|<|文|件|夹|目|录|路|径|的|了|操|作|完|成|保|存|到|在|里|内|上|后|前|左|右|等|和|与|或|及|并|还|也|就|都|而|且|之|从|时|已|被|给|于|过|有|很|将|会|可|能|所|多|大|小|新|旧|好|坏|高|低|长|短|快|慢|重|轻|深|浅|冷|热|用|为|是|不|再|更|最|太|非|常|只|但|却|然|否|则|即|若|如|$)/g,
      // 直接出现的用户目录路径（文件）: ~/path/to/file.ext
      /~\/(?:[a-zA-Z0-9._-]+\/)+[a-zA-Z0-9._-]+\.[a-zA-Z0-9]+/g,
      // 直接出现的用户目录路径（目录）: ~/path/to/directory 或 ~/path/to/directory/
      /~\/[a-zA-Z0-9._-]+(?:\/[a-zA-Z0-9._-]+)*\/?(?=\s|下|中|，|。|！|？|；|：|<|文|件|夹|目|录|路|径|的|了|操|作|完|成|保|存|到|在|里|内|上|后|前|左|右|等|和|与|或|及|并|还|也|就|都|而|且|之|从|时|已|被|给|于|过|有|很|将|会|可|能|所|多|大|小|新|旧|好|坏|高|低|长|短|快|慢|重|轻|深|浅|冷|热|用|为|是|不|再|更|最|太|非|常|只|但|却|然|否|则|即|若|如|$)/g
    ];

    pathPatterns.forEach((pattern, index) => {
      result = result.replace(pattern, (match, group1) => {
        // 对于code标签模式，使用group1；对于直接模式，使用match
        const path = index === 0 ? group1 : match;
        const trimmedPath = path.trim();
        
        // 检查是否已经被处理过
        if (result.includes(`data-file-path="${trimmedPath.replace(/"/g, '&quot;').replace(/'/g, '&#39;')}"`)) {
          return match;
        }
        
        if (isValidPath(trimmedPath).isValid) {
          // 只生成按钮容器，文件存在性检查将在后续异步进行
          // 如果文件不存在，后续检查时会移除整个容器
          // 对于code标签，只替换路径部分；对于直接路径，替换整个匹配
          if (index === 0) {
            return `<code>${generateFilePathContainer(trimmedPath)}</code>`;
          } else {
            return generateFilePathContainer(trimmedPath);
          }
        }
        
        return match;
      });
    });

    // 恢复代码块内容
    result = result.replace(/__CODE_BLOCK_(\d+)__/g, (match, index) => {
      return codeBlocks[parseInt(index)] || match;
    });

  } catch (error) {
    console.warn('处理文件路径时出错:', error);
    return html; // 出错时返回原始HTML
  }

  return result;
};

// 生成文件路径容器HTML的辅助函数（同步版本，后续异步检查）
const generateFilePathContainer = (path: string): string => {
  const escapedPath = path.replace(/"/g, '&quot;').replace(/'/g, '&#39;');
  const pathInfo = isValidPath(path);
  const isDirectory = pathInfo.isDirectory;
  
  if (isDirectory) {
    // 目录路径：只显示打开目录按钮
    return `<span class="file-path-container" data-file-path="${escapedPath}" data-file-checked="false" data-is-directory="true">
      <span class="file-path-text">${path}</span>
      <button class="file-path-btn file-location-btn" data-path="${escapedPath}" title="打开目录" style="opacity: 0.5;">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-icon lucide-folder"><path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z"/></svg>
      </button>
    </span>`;
  } else {
    // 文件路径：显示打开文件和打开目录两个按钮
    return `<span class="file-path-container" data-file-path="${escapedPath}" data-file-checked="false" data-is-directory="false">
      <span class="file-path-text">${path}</span>
      <button class="file-path-btn file-open-btn" data-path="${escapedPath}" title="打开文件" style="opacity: 0.5;">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-icon lucide-file"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/><path d="M14 2v4a2 2 0 0 0 2 2h4"/></svg>
      </button>
      <button class="file-path-btn file-location-btn" data-path="${escapedPath}" title="打开文件所在目录" style="opacity: 0.5;">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-icon lucide-folder"><path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z"/></svg>
      </button>
    </span>`;
  }
};

// 定义工具调用类型
interface ToolCall {
  id: string;
  name: string;
  displayName: string; // 显示用的名称（如"正在使用工具: xxx..."）
  args?: any; // 工具参数 (输入)
  result?: any; // 工具调用结果 (输出)
  status: 'calling' | 'completed'; // 工具调用状态
  expanded?: boolean; // 参数是否展开
}

// MCP下载请求接口
interface McpDownloadRequest {
  toolName: string;
  callbackId: string;
  toolArgs: any;
  projectUUId?: string;
  originalToolName?: string;
  downloadStatus?: 'pending' | 'downloading' | 'installing' | 'activating' | 'completed' | 'failed';
  timeoutId?: NodeJS.Timeout | undefined; // 明确指定类型为 NodeJS.Timeout | undefined
  serverInfo?: {
    packageName?: string;
    serverKey?: string;
    serverName?: string;
    projectUUId?: string;
    projectData?: any;
    localServerData?: any;
  };
}

// 定义消息类型
interface ChatMessage {
  key: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  reasoning_content?: string; // 添加reasoning内容
  thinking?: boolean; // 是否正在思考
  time?: number; // 思考用时（秒）
  tool_calls?: ToolCall[]; // 修改为工具调用对象数组
  emotion?: string; // AI的情感状态
  tts_text?: string; // TTS文本
  audio_data?: ArrayBuffer[]; // 音频数据数组
  mcpDownloadRequest?: McpDownloadRequest; // MCP下载请求
}

/**
 * AI对话组件 (使用 @ant-design/x 重构)
 * 显示与AI进行的对话内容，带头像、页眉和页脚 (使用 Bubble avatar prop)
 * 使用forwardRef和useImperativeHandle暴露startConversation方法
 */
const AiChat = forwardRef<AiChatRef, AiChatProps>(({ query, onComplete }, ref) => {
  // 使用语音管理器
  const { addAudio, endStream, stop, getAllAudioData } = useVoiceManager();

  // 获取文件路径上下文
  const { droppedFilePath, setDroppedFilePath } = useContext(FilePathContext);

  // --- Local State Management ---
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { systemInfo } = useSystemStore();

  const [messageHistory, setMessageHistory] = useState<ChatMessage[]>([
    { 
      role: 'system', 
      content: `你是Aido，由硅基极客研发的一个高效AI助手，擅长调用各种工具帮助用户解决问题或回答疑问。请保持回答简洁、实用，并尽可能提供准确信息。${useToolStore.getState().isToolsEnabled()? `我的操作系统是${systemInfo.platform}，我的系统架构是：${systemInfo.arch}，我的用户主目录是${systemInfo.homePath}。` : ''}`, 
      key: 'system', 
      timestamp: Date.now() 
    }
  ]);
  const [streamingText, setStreamingText] = useState<string>('');
  const [resetEvent, setResetEvent] = useState<number>(0); // Keep if downstream effects depend on it
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const isInitialQuerySent = useRef(false);
  const hasCodeBlocksToProcess = useRef(false);
  const themeStyleRef = useRef<HTMLLinkElement | null>(null);
  const currentMessageId = useRef<string>('');
  const mermaidRenderTimerRef = useRef<number | null>(null);
  // Active chat service instance ref
  const chatServiceRef = useRef<NextCharService | null>(null);
  
  // 添加reasoning相关的state
  const [openReasoningMap, setOpenReasoningMap] = useState<{[key: string]: boolean}>({});
  const [streamingReasoning, setStreamingReasoning] = useState<string>('');
  
  // 添加思考时间记录
  const startTimeRef = useRef<number | null>(null);
  
  // 添加reasoning完成状态跟踪
  const [reasoningCompletedMap, setReasoningCompletedMap] = useState<{[key: string]: boolean}>({});
  const reasoningTimeoutRef = useRef<{[key: string]: NodeJS.Timeout}>({});
  
  // 添加reasoning阶段跟踪，避免重复处理
  const reasoningPhaseRef = useRef<{[key: string]: boolean}>({});

  // 添加工具调用历史状态
  const [currentToolCalls, setCurrentToolCalls] = useState<ToolCall[]>([]);
  
  // 添加一个ref来跟踪当前消息ID，避免在递归调用时重置工具历史
  const activeMessageId = useRef<string>('');
  
  // 添加ref来确保在异步回调中能够访问到最新的工具调用历史
  const currentToolCallsRef = useRef<ToolCall[]>([]);

  // 添加工具调用展开状态管理
  const [toolCallsExpandedMap, setToolCallsExpandedMap] = useState<{[messageKey: string]: {[toolId: string]: boolean}}>({});

  // 添加复制成功状态管理
  const [copySuccessMap, setCopySuccessMap] = useState<{[key: string]: boolean}>({});

  // 添加情感和音频状态管理
  const [audioDataMap, setAudioDataMap] = useState<{[messageKey: string]: ArrayBuffer[]}>({});
  
  // 音频队列管理器实例
  const [finalAudioData, setFinalAudioData] = useState<ArrayBuffer[]>([]);

  // 过滤流式文本中的工具调用相关内容
  const filterToolCallsFromText = useCallback((text: string): string => {
    if (!text) return text;
    
    // 过滤掉工具调用相关的文本模式
    const filteredText = text
      // 移除 "*正在使用工具: xxx...*" 这样的文本
      .replace(/\*正在使用工具:\s*[^*]*\.\.\.\*/g, '')
      // 移除 "*工具: xxx...*" 这样的文本
      .replace(/\*工具:\s*[^*]*\.\.\.\*/g, '')
      // 移除 "*Using tool: xxx...*" 这样的英文文本
      .replace(/\*Using tool:\s*[^*]*\.\.\.\*/g, '')
      // 移除 "*Tool: xxx...*" 这样的英文文本
      .replace(/\*Tool:\s*[^*]*\.\.\.\*/g, '')
      // 移除多余的空行
      .replace(/\n\s*\n\s*\n/g, '\n\n')
      // 移除开头和结尾的空白字符
      .trim();
    
    return filteredText;
  }, []);

  // 处理reasoning展开/收起
  const handleOpenReasoning = useCallback((messageKey: string) => {
    setOpenReasoningMap(prev => {
      const newMap = {
        ...prev,
        [messageKey]: !prev[messageKey]
      };
      return newMap;
    });
  }, [openReasoningMap]);

  // 处理工具调用展开/收起
  const handleToggleToolCall = useCallback((messageKey: string, toolId: string) => {
    setToolCallsExpandedMap(prev => ({
      ...prev,
      [messageKey]: {
        ...prev[messageKey],
        [toolId]: !prev[messageKey]?.[toolId]
      }
    }));
  }, []);

  // 处理打开文件 - 移动到组件内部
  const handleOpenFile = useCallback(async (filePath: string) => {
    try {
      const success = await window.electron.files.open(filePath);
      if (!success) {
        console.error('打开文件失败:', filePath);
      }
    } catch (error) {
      console.error('打开文件时出错:', error);
    }
  }, []);

  // 处理打开文件所在目录 - 移动到组件内部
  const handleOpenFileLocation = useCallback(async (filePath: string) => {
    try {
      const success = await window.electron.files.openLocation(filePath);
      if (!success) {
        console.error('打开文件所在目录失败:', filePath);
      }
    } catch (error) {
      console.error('打开文件所在目录时出错:', error);
    }
  }, []);

  // 为文件路径按钮添加事件监听器 - 移动到组件内部并更新
  const attachFilePathListeners = useCallback(() => {
    // 查找所有文件路径按钮
    const openButtons = document.querySelectorAll('.file-open-btn');
    const locationButtons = document.querySelectorAll('.file-location-btn');
    
    // 立即移除所有按钮的title属性，防止默认tooltip显示
    [...openButtons, ...locationButtons].forEach((button) => {
      const htmlButton = button as HTMLElement;
      const title = htmlButton.getAttribute('title');
      if (title && !(htmlButton as any)._originalTitle) {
        (htmlButton as any)._originalTitle = title;
        htmlButton.removeAttribute('title');
      }
    });
    
    // 动态定位tooltip的函数
    const positionTooltip = (button: HTMLElement, tooltip: HTMLElement) => {
      const buttonRect = button.getBoundingClientRect();
      const bubbleContainer = button.closest('.ant-bubble-content');
      const containerRect = bubbleContainer?.getBoundingClientRect();
      
      if (!containerRect) return;
      
      // 计算tooltip的理想位置
      const tooltipWidth = tooltip.offsetWidth || 180; // 使用实际宽度或默认值
      const tooltipHeight = tooltip.offsetHeight || 30;
      
      // 默认位置：按钮上方居中
      let left = buttonRect.left + (buttonRect.width / 2) - (tooltipWidth / 2);
      let top = buttonRect.top - tooltipHeight - 6;
      
      // 检查右边界
      if (left + tooltipWidth > containerRect.right) {
        left = containerRect.right - tooltipWidth - 8;
      }
      
      // 检查左边界
      if (left < containerRect.left) {
        left = containerRect.left + 8;
      }
      
      // 检查上边界，如果超出则显示在按钮下方
      if (top < containerRect.top) {
        top = buttonRect.bottom + 6;
      }
      
      // 应用位置
      tooltip.style.left = `${left}px`;
      tooltip.style.top = `${top}px`;
      tooltip.style.transform = 'none';
    };

    // 全局tooltip管理器 - 确保同时只有一个tooltip显示
    let activeTooltip: HTMLElement | null = null;
    let showTimer: NodeJS.Timeout | null = null;
    let hideTimer: NodeJS.Timeout | null = null;

    // 清理活动tooltip的函数
    const clearActiveTooltip = () => {
      if (showTimer) {
        clearTimeout(showTimer);
        showTimer = null;
      }
      if (hideTimer) {
        clearTimeout(hideTimer);
        hideTimer = null;
      }
      
      if (activeTooltip) {
        activeTooltip.style.opacity = '0';
        const tooltipToRemove = activeTooltip;
        setTimeout(() => {
          if (tooltipToRemove && tooltipToRemove.parentNode) {
            tooltipToRemove.parentNode.removeChild(tooltipToRemove);
          }
        }, 200);
        activeTooltip = null;
      }
    };
    
    // 为按钮添加鼠标事件
    const addTooltipEvents = (button: HTMLElement) => {
      // 移除旧的事件监听器（如果存在）
      if ((button as any)._showTooltip) {
        button.removeEventListener('mouseenter', (button as any)._showTooltip);
      }
      if ((button as any)._hideTooltip) {
        button.removeEventListener('mouseleave', (button as any)._hideTooltip);
      }
      
      const showTooltip = () => {
        // 立即清理任何现有的tooltip
        clearActiveTooltip();
        
        const title = (button as any)._originalTitle;
        if (!title) return;
        
        // 延迟显示，避免快速移入移出时闪烁
        showTimer = setTimeout(() => {
          // 再次检查是否还需要显示tooltip（用户可能已经移开鼠标）
          if (!button.matches(':hover')) return;
          
          // 创建tooltip元素
          const tooltipElement = document.createElement('div');
          tooltipElement.className = 'custom-tooltip';
          tooltipElement.textContent = title;
          tooltipElement.style.cssText = `
            position: fixed;
            background-color: rgba(0, 0, 0, 0.85);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            line-height: 1.3;
            white-space: nowrap;
            z-index: 1000;
            pointer-events: none;
            max-width: 180px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            opacity: 0;
            transition: opacity 0.2s ease;
          `;
          
          // 检查暗色模式
          if (document.documentElement.classList.contains('dark')) {
            tooltipElement.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
            tooltipElement.style.color = 'black';
            tooltipElement.style.boxShadow = '0 2px 8px rgba(255, 255, 255, 0.1)';
          }
          
          document.body.appendChild(tooltipElement);
          activeTooltip = tooltipElement;
          
          // 定位tooltip
          positionTooltip(button, tooltipElement);
          
          // 显示动画
          requestAnimationFrame(() => {
            if (tooltipElement && tooltipElement === activeTooltip) {
              tooltipElement.style.opacity = '1';
            }
          });
        }, 300); // 300ms延迟，避免快速移入移出时闪烁
      };
      
      const hideTooltip = () => {
        // 取消显示计时器
        if (showTimer) {
          clearTimeout(showTimer);
          showTimer = null;
        }
        
        // 延迟隐藏，给用户一点时间移动鼠标
        hideTimer = setTimeout(() => {
          clearActiveTooltip();
        }, 100);
      };
      
      // 保存事件处理器引用，用于后续清理
      (button as any)._showTooltip = showTooltip;
      (button as any)._hideTooltip = hideTooltip;
      
      button.addEventListener('mouseenter', showTooltip);
      button.addEventListener('mouseleave', hideTooltip);
      
      // 标记已绑定事件
      (button as any)._tooltipEventsAttached = true;
    };
    
    // 为按钮添加点击事件
    const addClickEvents = (button: HTMLElement) => {
      // 移除旧的点击事件监听器（如果存在）
      if ((button as any)._clickHandler) {
        button.removeEventListener('click', (button as any)._clickHandler);
      }
      
      const path = button.getAttribute('data-path');
      if (!path) return;
      
      const clickHandler = (e: Event) => {
        e.preventDefault();
        e.stopPropagation();
        
        // 点击时立即隐藏tooltip
        clearActiveTooltip();
        
        // 获取路径容器，判断是文件还是目录
        const container = button.closest('.file-path-container') as HTMLElement;
        const isDirectory = container?.getAttribute('data-is-directory') === 'true';

        if (button.classList.contains('file-open-btn')) {
          // 打开文件按钮（只有文件路径才有这个按钮）
          handleOpenFile(path);
        } else if (button.classList.contains('file-location-btn')) {
          if (isDirectory) {
            // 目录路径：直接打开目录本身
            handleOpenFile(path); // 使用 handleOpenFile 函数
          } else {
            // 文件路径：打开文件所在目录
            handleOpenFileLocation(path);
          }
        }
      };
      
      // 保存事件处理器引用
      (button as any)._clickHandler = clickHandler;
      button.addEventListener('click', clickHandler);
      
      // 标记已绑定点击事件
      (button as any)._clickEventAttached = true;
    };
    
    openButtons.forEach((button) => {
      const htmlButton = button as HTMLElement;
      // 只有未绑定事件的按钮才添加事件
      if (!(htmlButton as any)._tooltipEventsAttached) {
        addTooltipEvents(htmlButton);
      }
      if (!(htmlButton as any)._clickEventAttached) {
        addClickEvents(htmlButton);
      }
    });
    
    locationButtons.forEach((button) => {
      const htmlButton = button as HTMLElement;
      // 只有未绑定事件的按钮才添加事件
      if (!(htmlButton as any)._tooltipEventsAttached) {
        addTooltipEvents(htmlButton);
      }
      if (!(htmlButton as any)._clickEventAttached) {
        addClickEvents(htmlButton);
      }
    });

    // 返回清理函数
    return () => {
      clearActiveTooltip();
    };
  }, [handleOpenFile, handleOpenFileLocation]);

  // --- Get active context from the store ---
  // Select the 'chat' object directly from the state
  const activeChat = useChatStore((state) => state.chat);

  // 初始化 Mermaid 配置
  const initializeMermaid = (isDark: boolean) => {
    mermaid.initialize({
      startOnLoad: false,
      theme: isDark ? 'dark' : 'default',
      // 可选：增加安全级别或其他配置
      // securityLevel: 'loose',
      // logLever: 'debug' // 用于调试
    });
  };

  // --- Modified startConversation --- 
  const startConversation = useCallback(async (inputQuery?: string) => {
    console.log('🔌 startConversation 被调用，输入查询:', inputQuery);
    console.log('🔌 当前droppedFilePath状态:', droppedFilePath);
    
    // TODO: 这里需要实现原有的消息发送逻辑
    // 暂时只是记录日志，需要您提供原有的实现方式
    if (inputQuery && inputQuery.trim()) {
      console.log('🔌 需要发送消息:', inputQuery.trim());
    }
    const textToQuery = inputQuery || query;

    let finalQuery = textToQuery;
    console.log('🔌 初始finalQuery:', finalQuery);

    if (!finalQuery) return;
    if (!activeChat) { // Check if context is available
      setError("Chat context is not available. Please select or create a chat.");
      setLoading(false);
      return;
    }
    const toolStore = useToolStore.getState();
    if(toolStore.isToolsEnabled()) {
      finalQuery = `${finalQuery}`
    }

    // 检查是否有拖拽的文件路径需要拼接
    if (droppedFilePath) {
      const pathArr=droppedFilePath.split(',');
      if(pathArr.length>1) {
        finalQuery = `${finalQuery}
待处理文件：
${pathArr.join('\n ')}`;
        
      }else{
        finalQuery = `${finalQuery}
待处理文件：
${droppedFilePath}`;
      }

      // 拼接后立即清除文件路径，确保只拼接一次
      setDroppedFilePath(null);
    }

    setLoading(true);
    setError(null);
    setStreamingText('');
    
    // 记录开始时间
    startTimeRef.current = Date.now();

    // 重置工具调用历史
    setCurrentToolCalls([]);
    currentToolCallsRef.current = [];
    
    // 清理音频相关状态
    setFinalAudioData([]);
    stop();
    // 在AI对话模式下，复用现有连接，不重新建立连接
    const userMessageId = `user-${Date.now()}`;
    const aiMessageId = `ai-${Date.now()}`;
    currentMessageId.current = aiMessageId;

    const newUserMessage: ChatMessage = {
      key: userMessageId,
      role: 'user',
      content: finalQuery,
      timestamp: Date.now(),
    };
    const placeholderAiMessage: ChatMessage = {
      key: aiMessageId,
      role: 'assistant',
      content: '', // Placeholder
      timestamp: Date.now(),
      reasoning_content: '', // 添加测试reasoning内容
      thinking: false,
      tool_calls: [] // 初始化工具调用历史
    };

    // Update local history immediately for UI responsiveness
    const currentHistory = [...messageHistory, newUserMessage, placeholderAiMessage];
    setMessageHistory(currentHistory);

    // Format messages for the service
    const messagesToService: IChatRequestMessage[] = messageHistory
      .concat(newUserMessage) // Include the new user message
      .map((msg) => ({
        role: msg.role,
        content: msg.content, // Assuming string content for now
      }));
      // Add system message if applicable based on context
      const systemMessage = activeChat.systemMessage;
      if (systemMessage) {
          messagesToService.unshift({ role: 'system', content: systemMessage });
      }

    try {
      // 强制使用WebSocket模式，不再依赖provider和model配置
      // 创建简化的WebSocket provider配置
      const wsProviderConfig = {
        name: 'WebSocket',
        isReady: true
      };
      
      // 创建简化的模型配置
      const wsModelConfig = {
        name: 'websocket-model',
        isReady: true,
        capabilities: {
          tools: { enabled: toolStore.isToolsEnabled() }
        }
      };

      // --- Create chat context object using WebSocket configs ---
      const chatContext: IChatContext = {
        getActiveChat: () => activeChat as IChat,
        getProvider: () => wsProviderConfig as any,
        getModel: () => wsModelConfig as any,
        getSystemMessage: () => activeChat.systemMessage || null,
        getTemperature: () => activeChat.temperature ?? DEFAULT_TEMPERATURE,
        getMaxTokens: () => activeChat.maxTokens || DEFAULT_MAX_TOKENS,
        getChatContext: () => activeChat.context || '',
        getCtxMessages: (msgId?: string) => { 
          console.warn('IChatContext.getCtxMessages not fully implemented in AiChat component');
          return []; 
        },
        isStream: () => activeChat.stream ?? true,
        isReady: () => true, // WebSocket always ready if service loads
      };
      // --- Use the factory to get the service instance --- 
      // 每次对话都创建新的服务实例，确保连接状态干净
      const chatService = createChatService(chatContext);
      if (!chatService) {
        throw new Error('Could not create chat service for the current context.');
      }
      chatServiceRef.current = chatService;
      // 确保连接可用
      try {
        if (chatService && typeof (chatService as any).ensureConnection === 'function') {
          await (chatService as any).ensureConnection();
        }
      } catch (error) {
        console.error('❌ WebSocket连接失败:', error);
        // 如果连接失败，清空chatServiceRef并重新创建
        chatServiceRef.current = null;
        throw new Error('WebSocket连接失败，请检查网络连接后重试');
      }

      // Configure Callbacks with Types
      chatService.onReading((chunk: string, reasoning?: string, emotion?: string, audioData?: ArrayBuffer) => {
        
        // 获取深度思考开关状态
        const isDeepThinkingEnabled = toolStore.isDeepThinkingEnabled();
        // 处理音频数据 - 使用语音管理器
        if (audioData) {
          // 立即添加到队列并自动播放
          addAudio(audioData);
          
          // 为了兼容性，也更新原有的数据结构（但不在UI中渲染分段音频）
          setAudioDataMap((prev) => ({
            ...prev,
            [aiMessageId]: [...(prev[aiMessageId] || []), audioData]
          }));
        }
        
        // 只有在深度思考开关启用时才处理reasoning内容
        if (reasoning && isDeepThinkingEnabled) {
          // 如果有reasoning内容，更新reasoning状态
          setStreamingReasoning((prev) => prev + reasoning);
          // 同时更新消息历史中的reasoning内容
          setMessageHistory((prev) =>
            prev.map((msg) =>
              msg.key === aiMessageId ? { 
                ...msg, 
                reasoning_content: (msg.reasoning_content || '') + reasoning,
                thinking: true
              } : msg
            )
          );
          
          // 标记当前消息正在进行reasoning阶段
          if (!reasoningPhaseRef.current[aiMessageId]) {
            reasoningPhaseRef.current[aiMessageId] = true;
          }
          
          // 清除之前的定时器
          if (reasoningTimeoutRef.current[aiMessageId]) {
            clearTimeout(reasoningTimeoutRef.current[aiMessageId]);
          }
          
          // 设置新的定时器，如果500ms内没有新的reasoning内容，则认为reasoning完成
          reasoningTimeoutRef.current[aiMessageId] = setTimeout(() => {
            // 只有在reasoning阶段且尚未标记为完成时才处理
            if (reasoningPhaseRef.current[aiMessageId] && !reasoningCompletedMap[aiMessageId]) {
              console.log('Reasoning phase completed for message:', aiMessageId);
              setReasoningCompletedMap(prev => ({
                ...prev,
                [aiMessageId]: true
              }));
              
              // 计算reasoning思考时间
              const reasoningTime = startTimeRef.current ? Math.round((Date.now() - startTimeRef.current) / 1000) : null;
              
              // 更新消息状态，标记thinking为false（reasoning完成）并保存时间
              setMessageHistory((prev) =>
                prev.map((msg) =>
                  msg.key === aiMessageId ? { 
                    ...msg, 
                    thinking: false,
                    time: reasoningTime
                  } : msg
                )
              );
              
              // 移除自动收起逻辑，让用户手动控制展开/收起状态
              // 确保reasoning完成后保持展开状态，用户可以查看完整的思考过程
              setOpenReasoningMap(prev => ({
                ...prev,
                [aiMessageId]: true // 设置为展开状态而不是收起
              }));
              
              // 清理标记
              delete reasoningPhaseRef.current[aiMessageId];
            }
            
            // 清理定时器引用
            delete reasoningTimeoutRef.current[aiMessageId];
          }, 500); // 增加到500ms延迟，减少误触发
        } else if (chunk) {
          // 普通内容（只有当chunk有内容时才添加）
          console.log('🔄 接收到流式内容chunk:', chunk);
          setStreamingText((prev) => {
            const newText = prev + chunk;
            console.log('🔄 更新streamingText:', newText.length, '字符');
            return newText;
          });
        }
        // 注意：情感和音频数据的处理在上面已经完成，不依赖于reasoning分支
      });

      // --- Use IChatResponseMessage for onComplete result type ---
      chatService.onComplete(async (result: IChatResponseMessage) => {
        console.log('Chat completed:', result);
        console.log('当前工具调用历史:', currentToolCalls);
        console.log('🎵 对话完成时的finalAudioData长度:', finalAudioData.length);
        setLoading(false);
        setStreamingReasoning(''); // 清理reasoning流状态
        // 对话完成后清空chatServiceRef，确保下次创建新的连接
        chatServiceRef.current = null;
        
        if (result.error) {
          const errorMsg = result.error.message || 'An unknown error occurred.';
          setError(errorMsg);
          setMessageHistory((prev) =>
            prev.map((msg) =>
              msg.key === aiMessageId ? { 
                ...msg, 
                content: `Error: ${errorMsg}`, 
                thinking: false
              } : msg
            )
          );
          // 出错时也停止音频播放
          stop();
        } else {
          // 对话完成时，先处理剩余的音频数据
          await endStream();
          
          // 直接更新消息内容和音频数据
          // 使用语音管理器的已处理音频数据
          const processedAudioData = getAllAudioData();
          console.log('🎵 最终音频数据长度:', processedAudioData.length);
          
          setMessageHistory((prev) =>
            prev.map((msg) =>
              msg.key === aiMessageId ? { 
                ...msg, 
                content: result.content || '',
                thinking: false,
                audio_data: processedAudioData.length > 0 ? [...processedAudioData] : undefined // 使用已处理的WAV音频数据
              } : msg
            )
          );
          
          console.log('对话完成，工具调用状态已在各自完成时更新');
          // 对话完成后，保持processedQueryRef不变，这样相同的query不会被重复处理
          // 只有当用户输入新的query时，才会触发新的对话
          console.log('🎯 对话完成，当前processedQuery:', processedQueryRef.current);
          // 通知搜索栏重置状态，但不返回主页
          if (onComplete) {
            onComplete();
          }
          
          // 在消息完成后延迟一点时间添加文件路径按钮事件监听器
          setTimeout(() => {
            console.log('在消息完成后添加文件路径按钮事件监听器');
            attachFilePathListeners();
          }, 300);
        }
        // Optional: Update tokens in chat store or state
         if (result.inputTokens || result.outputTokens) {
             // Example: updateChat(activeChat.getActiveChat().id, { tokensUsed: ... })
         }
      });

      chatService.onError((err: Error, aborted: boolean) => {
        console.error('NextChatService Error:', err, 'Aborted:', aborted);
        const errorMsg = aborted ? "Request aborted." : (err.message || 'Chat service error');
        setError(errorMsg);
        setLoading(false);
        // 出错时也清空chatServiceRef，确保下次创建新的连接
        chatServiceRef.current = null;
        setMessageHistory((prev) =>
          prev.map((msg) =>
            msg.key === aiMessageId ? { ...msg, content: `Error: ${errorMsg}` } : msg
          )
        );
      });

      chatService.onToolCalls((toolName: string, toolArgs?: any) => {
        console.log(`Tool call requested: ${toolName}`, 'args:', toolArgs);
        console.log('工具调用前的currentToolCalls:', currentToolCalls);
        const newToolCall = `正在使用工具: ${toolName}`;
        
        setCurrentToolCalls(prev => {
          // 检查是否已存在相同工具名称的调用
          const existingIndex = prev.findIndex(tool => tool.name === toolName && tool.status === 'calling');
          
          if (existingIndex >= 0 && toolArgs) {
            // 如果存在相同工具且提供了参数，更新现有的工具调用
            const updatedToolCalls = [...prev];
            updatedToolCalls[existingIndex] = {
              ...updatedToolCalls[existingIndex],
              args: toolArgs,
              status: 'calling' as const
            };
            console.log('更新工具调用参数:', updatedToolCalls);
            currentToolCallsRef.current = updatedToolCalls;
            
            // 立即更新消息历史，触发重新渲染
            setMessageHistory((prevHistory) =>
              prevHistory.map((msg) =>
                msg.key === aiMessageId ? { 
                  ...msg, 
                  tool_calls: updatedToolCalls
                } : msg
              )
            );
            
            return updatedToolCalls;
          } else if (existingIndex < 0) {
            // 如果不存在相同工具，创建新的工具调用
            const toolCall: ToolCall = {
              id: `${toolName}-${Date.now()}`,
              name: toolName,
              displayName: newToolCall,
              args: toolArgs, // 可能为空（第一次调用）或有值（第二次调用）
              status: 'calling' as const,
              expanded: false
            };
            const newToolCalls = [...prev, toolCall];
            console.log('创建新的工具调用:', newToolCalls);
            currentToolCallsRef.current = newToolCalls;
            
            // 立即更新消息历史，触发重新渲染
            setMessageHistory((prevHistory) =>
              prevHistory.map((msg) =>
                msg.key === aiMessageId ? { 
                  ...msg, 
                  tool_calls: newToolCalls
                } : msg
              )
            );
            
            return newToolCalls;
          }
          
          // 其他情况不做改变
          return prev;
        });
      });

      // 添加工具调用结果处理回调
      if (typeof chatService.onToolResult === 'function') {
        chatService.onToolResult((toolName: string, toolResult?: any) => {
          console.log(`Tool result received: ${toolName}`, 'result:', toolResult);
          console.log('工具调用结果的详细信息:', {
            toolName,
            resultType: typeof toolResult,
            resultKeys: toolResult ? Object.keys(toolResult) : null,
            isError: toolResult?.isError,
            content: toolResult?.content,
            result: toolResult?.result
          });
          
          setCurrentToolCalls(prev => {
            const updatedToolCalls = prev.map(toolCall => {
              if (toolCall.name === toolName && toolCall.status === 'calling') {
                return {
                  ...toolCall,
                  result: toolResult,
                  status: 'completed' as const, // 立即设置为完成状态
                  displayName: toolCall.displayName.replace('正在使用工具:', '已完成工具:')
                };
              }
              return toolCall;
            });
            console.log('更新工具调用结果:', updatedToolCalls);
            currentToolCallsRef.current = updatedToolCalls;
            
            // 立即更新消息历史，触发重新渲染
            setMessageHistory((prevHistory) =>
              prevHistory.map((msg) =>
                msg.key === aiMessageId ? { 
                  ...msg, 
                  tool_calls: updatedToolCalls
                } : msg
              )
            );
            
            return updatedToolCalls;
          });
        });
      }
      console.log(messagesToService, "messagesToService")
      // Initiate Chat
      await chatService.chat(messagesToService);

    } catch (err: any) {
      console.error('Failed to initiate chat:', err);
      setError(err.message || 'Failed to start chat');
      setLoading(false);
      // 出错时也清空chatServiceRef，确保下次创建新的连接
      chatServiceRef.current = null;
      setMessageHistory((prev) =>
        prev.map((msg) =>
          msg.key === aiMessageId ? { ...msg, content: `Error: ${err.message}` } : msg
        )
      );

      // 连接失败时重置输入框状态
      if (onComplete) {
        console.log('🔄 连接失败，重置输入框状态');
        onComplete();
      }

    }
  }, [query, messageHistory, onComplete, activeChat, droppedFilePath, setDroppedFilePath]);

  // 使用useImperativeHandle暴露方法给父组件
  useImperativeHandle(ref, () => ({
    startConversation,
    reset: async () => {
      // 同步清理WebSocket连接
      try {
        const { default: WebSocketChatService } = await import('../../../../llm/services/WebSocketChatService');
        WebSocketChatService.cleanup();
        console.log('🔌 AI聊天组件重置时已完全断开WebSocket连接');
      } catch (error) {
        console.error('清理WebSocket连接失败:', error);
      }
      
      // 确保chatServiceRef被清空
      if (chatServiceRef.current) {
        chatServiceRef.current.abort();
        chatServiceRef.current = null;
      }
      
      setMessageHistory([]);
      setStreamingText('');
      setStreamingReasoning(''); // 清理reasoning流状态
      setOpenReasoningMap({}); // 清理reasoning展开状态
      setReasoningCompletedMap({}); // 清理reasoning完成状态
      setCurrentToolCalls([]); // 清理工具调用历史
      currentToolCallsRef.current = []; // 清理工具调用历史ref
      setToolCallsExpandedMap({}); // 清理工具调用展开状态
      setCopySuccessMap({}); // 清理复制成功状态
      setAudioDataMap({}); // 清理音频数据
      setFinalAudioData([]); // 清理最终音频数据
      stop(); // 停止音频播放队列
      setLoading(false);
      setError(null);
      setResetEvent((prev) => prev + 1);
      startTimeRef.current = null; // 清理开始时间
      // 清理reasoning定时器
      Object.values(reasoningTimeoutRef.current).forEach(timeout => {
        clearTimeout(timeout);
      });
      reasoningTimeoutRef.current = {};
      // 清理reasoning阶段跟踪
      reasoningPhaseRef.current = {};
       // Potentially reset specific chat context details via useChatStore actions if needed
    }
  }), [startConversation]);

  // 更新高亮和 Mermaid 样式的函数
  const updateHighlightStyle = useCallback(() => {
    if (!themeStyleRef.current) return;

    const isDarkMode = document.documentElement.classList.contains('dark');
    const stylePath = isDarkMode
      ? '../styles/ai-chat/highlight/github-dark.min.css'
      : '../styles/ai-chat/highlight/github.min.css';

    // 只有当路径变化时才更新，避免不必要的闪烁
    if (themeStyleRef.current.getAttribute('href') !== stylePath) {
      themeStyleRef.current.href = stylePath;
    }

    // 更新 Mermaid 主题
    initializeMermaid(isDarkMode);
  }, []);

  // 根据当前主题模式动态加载highlight.js样式
  useEffect(() => {
    // 创建样式元素
    const styleLink = document.createElement('link');
    styleLink.rel = 'stylesheet';
    themeStyleRef.current = styleLink;

    // 初始加载样式
    updateHighlightStyle();

    // 添加到文档头部
    document.head.appendChild(styleLink);

    // 监听主题变化
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.type === 'attributes' &&
          mutation.attributeName === 'class' &&
          mutation.target === document.documentElement) {
          updateHighlightStyle();
        }
      });
    });

    // 观察html元素的class变化（当切换暗黑模式时会添加或移除dark类）
    observer.observe(document.documentElement, { attributes: true });

    // 添加滚动事件监听器，滚动时清理tooltip
    const handleScroll = () => {
      const tooltips = document.querySelectorAll('.custom-tooltip');
      tooltips.forEach(tooltip => {
        const htmlTooltip = tooltip as HTMLElement;
        htmlTooltip.style.opacity = '0';
        setTimeout(() => {
          if (htmlTooltip.parentNode) {
            htmlTooltip.parentNode.removeChild(htmlTooltip);
          }
        }, 100);
      });
    };

    // 监听容器滚动
    const chatContainer = chatContainerRef.current;
    if (chatContainer) {
      chatContainer.addEventListener('scroll', handleScroll, { passive: true });
    }

    // 清理函数
    return () => {
      observer.disconnect();
      if (themeStyleRef.current && document.head.contains(themeStyleRef.current)) {
        document.head.removeChild(themeStyleRef.current);
      }
      // 清理 Mermaid 渲染计时器
      if (mermaidRenderTimerRef.current) {
        clearTimeout(mermaidRenderTimerRef.current);
      }
      // 清理reasoning定时器
      Object.values(reasoningTimeoutRef.current).forEach(timeout => {
        clearTimeout(timeout);
      });
      reasoningTimeoutRef.current = {};
      // 清理reasoning阶段跟踪
      reasoningPhaseRef.current = {};
      
      // 移除滚动事件监听器
      if (chatContainer) {
        chatContainer.removeEventListener('scroll', handleScroll);
      }
      
      // 清理所有残留的tooltip
      const existingTooltips = document.querySelectorAll('.custom-tooltip');
      existingTooltips.forEach(tooltip => {
        if (tooltip.parentNode) {
          tooltip.parentNode.removeChild(tooltip);
        }
      });
      

    };
  }, [updateHighlightStyle]);

  // AI聊天页面加载时设置窗口为非紧凑模式
  useEffect(() => {
    if (window.electron?.window?.setCompactMode) {
      window.electron.window.setCompactMode(false);
    }
  }, []); // 空依赖数组，只在组件挂载时执行一次

  // 处理MCP技能下载请求的函数
  const handleMcpToolDownloadRequest = useCallback((data: {
    toolName: string;
    toolArgs: any;
    callbackId: string;
    message: string;
    projectUUId?: string;
    originalToolName?: string;
    autoTriggered?: boolean; // 新增：标识是否为自动触发
    serverInfo?: {
      packageName?: string;
      serverKey?: string;
      serverName?: string;
      projectUUId?: string;
      projectData?: any;
      localServerData?: any;
    };
  }) => {
    console.log('收到MCP技能下载请求:', data);
    console.log('🔍 serverInfo结构:', JSON.stringify(data.serverInfo, null, 2));

    // 使用函数式更新来检查重复请求
    setMessageHistory(prev => {
      // 检查是否已经存在相同的下载请求，避免重复
      const existingRequest = prev.find(msg =>
        msg.mcpDownloadRequest?.callbackId === data.callbackId
      );

      if (existingRequest) {
        console.log('已存在相同的下载请求，跳过创建新的请求');
        return prev;
      }

      // 创建AI回复消息，显示MCP服务器下载请求
      const aiMessage: ChatMessage = {
        role: 'assistant',
        content: '', // 初始为空，避免显示下载文字
        key: `ai-mcp-response-${Date.now()}`,
        timestamp: Date.now(),
        mcpDownloadRequest: {
          toolName: data.toolName,
          callbackId: data.callbackId,
          toolArgs: data.toolArgs,
          projectUUId: data.projectUUId,
          originalToolName: data.originalToolName,
          serverInfo: data.serverInfo,
          downloadStatus: 'pending'
        }
      };

      console.log('创建新的MCP下载请求消息:', aiMessage);
      return [...prev, aiMessage];
    });

    // 如果是自动触发的下载请求，确保界面滚动到底部显示
    if (data.autoTriggered) {
      setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
      }, 100);
    }

    // 设置30秒超时，自动取消下载请求
    const timeoutId = setTimeout(() => {
      setMessageHistory(prev => prev.map(m => {
        if (m.mcpDownloadRequest?.callbackId === data.callbackId &&
            (!m.mcpDownloadRequest.downloadStatus || m.mcpDownloadRequest.downloadStatus === 'pending')) {
          // 发送超时取消事件给后端
          if (window.electron?.crossWindow) {
            window.electron.crossWindow.emit('mcp:server-download-completed', {
              success: false,
              callbackId: data.callbackId,
              originalToolName: data.originalToolName || data.toolName,
              reason: 'timeout'
            });
          }

          return {
            ...m,
            mcpDownloadRequest: {
              ...m.mcpDownloadRequest,
              downloadStatus: 'failed' as const
            },
            content: 'MCP技能下载超时（30秒），已自动取消。'
          };
        }
        return m;
      }));
    }, 30000); // 30秒超时

    // 将超时ID存储到消息中，以便在用户手动操作时清除
    setMessageHistory(prev => prev.map(m => {
      if (m.mcpDownloadRequest?.callbackId === data.callbackId) {
        return {
          ...m,
          mcpDownloadRequest: {
            ...m.mcpDownloadRequest,
            timeoutId: timeoutId
          }
        };
      }
      return m;
    }));
  }, []); // 移除messageHistory依赖，避免回调函数重新创建

  // 获取MCP服务器名称的辅助函数
  const getMcpServerName = useCallback((mcpRequest: McpDownloadRequest): string => {
    // 尝试多个可能的名称来源
    const serverInfo = mcpRequest.serverInfo;

    // 优先使用本地服务器名称
    if (serverInfo?.localServerData?.name) {
      return serverInfo.localServerData.name;
    }

    // 其次使用项目数据名称
    if (serverInfo?.projectData?.name) {
      return serverInfo.projectData.name;
    }

    // 使用服务器名称
    if (serverInfo?.serverName) {
      return serverInfo.serverName;
    }

    // 使用项目UUID作为后备
    if (mcpRequest.projectUUId) {
      return mcpRequest.projectUUId;
    }

    // 最后的后备选项
    return '未知服务器';
  }, []);

  // 处理MCP服务器状态更新事件
  const handleMcpServerStatusUpdate = useCallback((data: {
    callbackId: string;
    status: 'pending' | 'downloading' | 'installing' | 'activating' | 'completed' | 'failed';
    message: string;
  }) => {
    console.log('🔄 收到MCP服务器状态更新:', data);
    console.log('🔄 状态详情:', { status: data.status, callbackId: data.callbackId, message: data.message });
    // 移除对messageHistory的直接引用，因为可能是过期的

    setMessageHistory(prev => {
      console.log('📋 开始更新消息历史，查找callbackId:', data.callbackId);
      console.log('📋 当前消息历史中的MCP请求:', prev.filter(msg => msg.mcpDownloadRequest).map(msg => ({
        key: msg.key,
        callbackId: msg.mcpDownloadRequest?.callbackId,
        status: msg.mcpDownloadRequest?.downloadStatus
      })));

      const updated = prev.map(msg => {
        if (msg.mcpDownloadRequest?.callbackId === data.callbackId) {
          console.log(`📝 找到匹配的消息，更新状态: ${msg.mcpDownloadRequest.downloadStatus} → ${data.status}`);

          // 如果状态是completed，保持下载请求但更新状态
          if (data.status === 'completed') {
            console.log('🎉 状态为completed，更新下载状态为completed');
            return {
              ...msg,
              mcpDownloadRequest: {
                ...msg.mcpDownloadRequest,
                downloadStatus: data.status as 'completed'
              },
              // 显示完成消息
              content: '✅ MCP服务器安装激活完成！'
            };
          }

          return {
            ...msg,
            mcpDownloadRequest: {
              ...msg.mcpDownloadRequest,
              downloadStatus: data.status as any
            }
          };
        }
        return msg;
      });

      const updatedMsg = updated.find(msg => msg.mcpDownloadRequest?.callbackId === data.callbackId);
      console.log('📋 消息历史更新完成，当前状态:', updatedMsg?.mcpDownloadRequest?.downloadStatus);
      console.log('📋 更新后的消息:', updatedMsg);

      if (!updatedMsg) {
        console.warn('⚠️ 没有找到匹配的消息！callbackId:', data.callbackId);
        console.warn('⚠️ 当前所有MCP请求:', updated.filter(msg => msg.mcpDownloadRequest).map(msg => ({
          key: msg.key,
          callbackId: msg.mcpDownloadRequest?.callbackId,
          status: msg.mcpDownloadRequest?.downloadStatus
        })));
      }

      return updated;
    });
  }, []);

  // 处理MCP技能下载完成事件的函数
  const handleMcpToolDownloadCompleted = useCallback((data: {
    toolName: string;
    success: boolean;
    callbackId: string;
  }) => {
    console.log('收到MCP技能下载完成事件:');

    // 延迟一段时间后，在对话框下方添加技能调用结果，而不是替换对话框
    setTimeout(() => {
      setMessageHistory(prev => [...prev, {
        role: 'assistant',
        content: data.success
          ? `✅ MCP服务器安装激活完成！正在重新尝试执行您的请求...`
          : `❌ MCP服务器安装失败，请稍后重试。`,
        key: `mcp-completion-${Date.now()}`,
        timestamp: Date.now()
      }]);

      // 如果成功，再添加一个成功提示消息
      if (data.success) {
        setTimeout(() => {
          setMessageHistory(prev => [...prev, {
            role: 'assistant',
            content: '🎉 MCP服务器已成功安装并激活，您现在可以继续使用相关技能了！',
            key: `mcp-success-${Date.now()}`,
            timestamp: Date.now()
          }]);
        }, 1000);
      }
    }, 2000); // 2秒后在下方添加消息
  }, []);

  // 设置MCP事件监听器
  useEffect(() => {
    // 注册MCP技能下载请求监听器
    let unsubscribeMcp: (() => void) | undefined;
    if (window.electron?.mcp?.onToolDownloadRequest) {
      unsubscribeMcp = window.electron.mcp.onToolDownloadRequest(handleMcpToolDownloadRequest);
    }

    // 注册MCP服务器状态更新监听器
    let unsubscribeStatusUpdate: (() => void) | undefined;
    if (window.electron?.crossWindow) {
      console.log('🔧 注册MCP服务器状态更新监听器');
      unsubscribeStatusUpdate = window.electron.crossWindow.on('mcp:server-status-update', handleMcpServerStatusUpdate);

      // 测试跨窗口通信是否工作
      window.electron.crossWindow.on('test-event', (data: any) => {
        console.log('🧪 收到测试事件:', data);
      });

      // 添加通用事件监听器来捕获所有事件
      window.electron.crossWindow.on('mcp:server-status-update', (data: any) => {
        console.log('🔄 直接收到状态更新事件:', data);
      });

      // 测试发送一个测试事件
      setTimeout(() => {
        if (window.electron?.crossWindow) {
          window.electron.crossWindow.emit('test-event', { message: '测试事件' });
        }
      }, 3000);
    } else {
      console.error('❌ window.electron.crossWindow 不可用');
    }

    // 注册MCP技能下载完成监听器
    let unsubscribeDownloadCompleted: (() => void) | undefined;
    if (window.electron?.crossWindow) {
      unsubscribeDownloadCompleted = window.electron.crossWindow.on('mcp:tool-download-completed', handleMcpToolDownloadCompleted);
    }

    // 注册跨窗口通信监听器（用于接收MCP下载请求）
    let unsubscribeCrossWindow: (() => void) | undefined;
    if (window.electron?.crossWindow) {
      unsubscribeCrossWindow = window.electron.crossWindow.on('mcp:tool-download-request', handleMcpToolDownloadRequest);
    }

    return () => {
      console.log('🧹 清理MCP事件监听器...');

      // 清理MCP监听器
      if (unsubscribeMcp) {
        unsubscribeMcp();
      }

      // 清理状态更新监听器
      if (unsubscribeStatusUpdate) {
        unsubscribeStatusUpdate();
      }

      // 清理下载完成监听器
      if (unsubscribeDownloadCompleted) {
        unsubscribeDownloadCompleted();
      }

      // 清理跨窗口通信监听器
      if (unsubscribeCrossWindow) {
        unsubscribeCrossWindow();
      }
    };
  }, []); // 移除依赖项，避免频繁重新注册事件监听器

  // 处理状态更新和UI反馈
  useEffect(() => {
    // Auto-scroll to bottom
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
    console.log(messageHistory, "messageHistory")
  }, [streamingText, messageHistory]);

  // 复制代码的处理函数
  const copyCodeToClipboard = useCallback((codeText: string, button: HTMLElement) => {
    navigator.clipboard.writeText(codeText).then(
      () => {
        // 临时改变按钮文本表示复制成功
        const originalText = button.textContent;
        button.textContent = '已复制';
        setTimeout(() => {
          button.textContent = originalText;
        }, 2000);
      },
      (err) => {
        console.error('无法复制文本: ', err);
      }
    );
  }, []);

  // 处理未处理的代码块
  const processNewCodeBlocks = useCallback(() => {
    // 查找所有未处理的代码块（非Mermaid）
    const unprocessedCodeBlocks = document.querySelectorAll(`pre.code-block:not(.${PROCESSED_CLASS})`);
    unprocessedCodeBlocks.forEach(block => {
      // 将block转换为HTMLElement类型
      const htmlBlock = block as HTMLElement;

      // 标记为已处理，避免重复处理
      htmlBlock.classList.add(PROCESSED_CLASS);

      // 获取代码内容
      const codeElement = htmlBlock.querySelector('code');
      const codeText = codeElement?.textContent || '';

      // 检查是否已经有header，避免重复添加
      if (!htmlBlock.querySelector('.code-block-header')) {
        // 创建顶部控制栏
        const headerBar = document.createElement('div');
        headerBar.className = 'code-block-header';

        // 获取语言信息
        const lang = htmlBlock.dataset.lang || '代码';

        // 创建语言标签
        const langLabel = document.createElement('div');
        langLabel.className = 'code-lang-label';
        langLabel.textContent = lang;
        headerBar.appendChild(langLabel);

        // 创建复制按钮
        const copyBtn = document.createElement('div');
        copyBtn.className = 'code-copy-btn';
        copyBtn.textContent = '复制';
        copyBtn.title = '复制代码';

        // 绑定点击事件
        copyBtn.addEventListener('click', () => {
          copyCodeToClipboard(codeText, copyBtn);
        });

        headerBar.appendChild(copyBtn);

        // 为header预留空间，添加到代码块中
        // htmlBlock.style.paddingTop = '40px';

        // 将控制栏添加到代码块前
        htmlBlock.insertBefore(headerBar, htmlBlock.firstChild);
      }
    });
  }, [copyCodeToClipboard]);

  // 处理未处理的Mermaid块
  const processMermaidBlocks = useCallback(async () => {
    const mermaidBlocks = document.querySelectorAll(`pre.language-mermaid:not(.${MERMAID_PROCESSED_CLASS})`);
    if (mermaidBlocks.length === 0) return;

    console.log(`发现 ${mermaidBlocks.length} 个未处理的 Mermaid 块`);

    // 使用 NodeListOf<Element> 替代 NodeListOf<HTMLElement>
    const blocksToRender: Element[] = Array.from(mermaidBlocks);

    blocksToRender.forEach(async (block) => {
      // 添加处理标记，防止重复渲染
      block.classList.add(MERMAID_PROCESSED_CLASS);

      const codeElement = block.querySelector('code');
      const mermaidCode = codeElement?.textContent || '';

      if (mermaidCode) {
        console.log('Attempting to render Mermaid with code:', mermaidCode);
        try {
          // 创建一个容器来渲染SVG
          const renderContainer = document.createElement('div');
          renderContainer.className = 'mermaid-container'; // 添加容器类名
          // 将容器插入到 pre 标签之前
          block.parentNode?.insertBefore(renderContainer, block);
          // 隐藏原始的 pre 标签
          (block as HTMLElement).style.display = 'none';

          // 使用 mermaid.render 生成 SVG
          const { svg } = await mermaid.render(`mermaid-graph-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`, mermaidCode);
          renderContainer.innerHTML = svg;
          console.log('Mermaid 图表渲染成功');

        } catch (error) {
          console.error('Mermaid 渲染失败:', error);
          // 可选：显示错误信息给用户
          const errorContainer = document.createElement('div');
          errorContainer.className = 'mermaid-error';
          errorContainer.textContent = `Mermaid 渲染出错: ${error instanceof Error ? error.message : String(error)}`;
          block.parentNode?.insertBefore(errorContainer, block);
           // 仍然隐藏原始的 pre 标签
          (block as HTMLElement).style.display = 'none';
        }
      }
    });
  }, []);

  // 当 messageHistory 更新后（意味着流式输出可能已完成并设置了最终内容），处理 Mermaid 块
  useEffect(() => {
    // 调用 processMermaidBlocks 来渲染任何新出现的、未处理的 Mermaid 块
    processMermaidBlocks();
    // 延迟绑定文件路径按钮事件监听器，确保DOM已更新
    const timer = setTimeout(() => {
      const cleanup = attachFilePathListeners();
      // 如果有清理函数，存储它以便在下次更新时调用
      return cleanup;
    }, 100);
    
    return () => {
      clearTimeout(timer);
    };
  }, [messageHistory, processMermaidBlocks, attachFilePathListeners]); // 依赖 messageHistory

  // 每次渲染后处理新的【普通】代码块
  useEffect(() => {
    // 只处理普通代码块，Mermaid 在流结束后通过上面的 useEffect 处理
    processNewCodeBlocks();
    // 只在loading为false（消息完成）时绑定文件路径按钮
    if (!loading) {
      const timer = setTimeout(() => {
        const cleanup = attachFilePathListeners();
        return cleanup;
      }, 100);
      
      return () => {
        clearTimeout(timer);
      };
    }
  }, [streamingText, processNewCodeBlocks, attachFilePathListeners, loading]);

  // 专门处理文件路径按钮绑定的effect - 只在消息完成时执行
  useEffect(() => {
    // 只在非loading状态下绑定文件路径按钮
    if (!loading) {
      const timer = setTimeout(() => {
        const cleanup = attachFilePathListeners();
        // 存储清理函数，在组件卸载或下次更新时调用
        if (cleanup && typeof cleanup === 'function') {
          return cleanup;
        }
      }, 200);
      
      return () => {
        clearTimeout(timer);
        // 这里无法直接调用cleanup，因为它是异步获取的
        // 但attachFilePathListeners内部的全局管理器会处理清理
      };
    }
  }, [messageHistory, loading, attachFilePathListeners]);

  // 当流式内容更新时，也检查并处理代码块
  useEffect(() => {
    if (streamingText.includes('```') && !hasCodeBlocksToProcess.current) {
      hasCodeBlocksToProcess.current = true;
      console.log('有新代码块');
      // if (streamingText.includes('```') && !hasCodeBlocksToProcess.current) {
      //   console.log('代码块结束');
      // }
    }
  }, [streamingText]);

  // 当resetEvent变化时清空消息历史
  useEffect(() => {
    if (resetEvent > 0) {
      setMessageHistory([]);
      setStreamingText('');
    }
  }, [resetEvent]);

  // 准备消息列表
  const messages = useMemo(() => {
    if (messageHistory.length <= 1) {
      return [];
    }
    const msgs = messageHistory.slice(1);
    return msgs.map((msg) => ({
      key: msg.key,
      role: msg.role,
      placement: msg.role === 'user' ? 'end' : 'start',
      // 为有MCP下载请求的消息添加特殊样式类，隐藏外层边框
      className: msg.mcpDownloadRequest ? 'mcp-download-message' : '',
      messageRender: () => {
        // 用户消息直接返回内容
        if (msg.role === 'user') {
          return msg.content;
        }

        // AI消息的统一渲染逻辑
        const isCurrentStreaming = msg.key === currentMessageId.current && loading;
        
        // 优先使用消息历史中的tool_calls，这样工具调用信息能立即显示
        const currentToolCalls = msg.tool_calls || [];
        
        // 修改空内容判断逻辑：只有在没有streamingText、streamingReasoning和工具调用信息时才显示加载动画
        const isStreamingEmpty = isCurrentStreaming && !streamingText && !streamingReasoning && currentToolCalls.length === 0;
        
        // 如果是当前流式消息且内容为空（包括没有工具调用），显示加载动画
        if (isStreamingEmpty) {
          return (
            <Typography>
              <div className="loading-container">
                <LoadingDots />
              </div>
            </Typography>
          );
        }

        // 确定当前显示的内容
        const currentContent = isCurrentStreaming ? streamingText : msg.content;
        const currentReasoningContent = isCurrentStreaming 
          ? (streamingReasoning || msg.reasoning_content || '') 
          : (msg.reasoning_content || '');
        
        // 获取当前深度思考开关状态
        const toolStore = useToolStore.getState();
        const isDeepThinkingEnabled = toolStore.isDeepThinkingEnabled();
        
        // 修复thinking状态判断逻辑，避免闪烁
        // 如果是流式传输且有reasoning内容但thinking为false，说明reasoning已完成但UI可能还在展开状态
        // 在这种情况下，我们应该保持展开状态直到用户手动收起
        const isThinking = isCurrentStreaming && msg.thinking;
        const shouldShowReasoningExpanded = isThinking || openReasoningMap[msg.key];

        // 对于流式内容，过滤掉工具调用相关文本
        const filteredCurrentContent = isCurrentStreaming 
          ? filterToolCallsFromText(currentContent || '')
          : (currentContent || '');

        return (
          <Typography>
            <div>
              {/* MCP下载请求UI - 优先显示，但完成时隐藏 */}
              {msg.mcpDownloadRequest && msg.mcpDownloadRequest.downloadStatus !== 'completed' && (
                <div className="flex items-start gap-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                  <div className="flex-shrink-0">
                    <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                      {(() => {
                        const status = msg.mcpDownloadRequest.downloadStatus;
                        const isLocal = msg.mcpDownloadRequest.serverInfo?.localServerData;
                        switch (status) {
                          case 'downloading': return '正在下载MCP服务器...';
                          case 'installing': return '正在安装MCP服务器...';
                          case 'activating': return '正在激活MCP服务器...';
                          case 'failed': return 'MCP服务器安装失败';
                          default: return isLocal ? 'MCP服务器激活请求' : 'MCP服务器下载请求';
                        }
                      })()}
                    </h4>
                    {(!msg.mcpDownloadRequest.downloadStatus || msg.mcpDownloadRequest.downloadStatus === 'pending') && (
                      <div className="flex gap-2">
                        <button
                          onClick={() => {
                            // 防止重复点击
                            const currentStatus = msg.mcpDownloadRequest!.downloadStatus;
                            if (currentStatus && currentStatus !== 'pending') {
                              return; // 如果状态不是pending，不处理点击
                            }

                            // 清除超时定时器
                            if (msg.mcpDownloadRequest!.timeoutId) {
                              clearTimeout(msg.mcpDownloadRequest!.timeoutId);
                            }

                            const isLocal = msg.mcpDownloadRequest!.serverInfo?.localServerData;
                            const initialStatus = isLocal ? 'activating' : 'downloading';
                            setMessageHistory(prev => prev.map(m => {
                              if (m.mcpDownloadRequest?.callbackId === msg.mcpDownloadRequest!.callbackId) {
                                return { ...m, mcpDownloadRequest: { ...m.mcpDownloadRequest, downloadStatus: initialStatus as 'activating' | 'downloading', timeoutId: undefined } };
                              }
                              return m;
                            }));
                            if (window.electron?.crossWindow) {
                              window.electron.crossWindow.emit('mcp:server-download-completed', {
                                success: true,
                                callbackId: msg.mcpDownloadRequest!.callbackId,
                                originalToolName: msg.mcpDownloadRequest!.originalToolName || msg.mcpDownloadRequest!.toolName,
                                serverInfo: msg.mcpDownloadRequest!.serverInfo
                              });
                            }
                          }}
                          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md"
                        >
                          {msg.mcpDownloadRequest.serverInfo?.localServerData ? '激活服务器' : '下载并安装'}
                        </button>
                        <button
                          onClick={() => {
                            // 防止重复点击
                            const currentStatus = msg.mcpDownloadRequest!.downloadStatus;
                            if (currentStatus && currentStatus !== 'pending') {
                              return; // 如果状态不是pending，不处理点击
                            }

                            // 清除超时定时器
                            if (msg.mcpDownloadRequest!.timeoutId) {
                              clearTimeout(msg.mcpDownloadRequest!.timeoutId);
                            }

                            // 立即更新UI状态，显示取消状态
                            setMessageHistory(prev => prev.map(m => {
                              if (m.mcpDownloadRequest?.callbackId === msg.mcpDownloadRequest!.callbackId) {
                                return {
                                  ...m,
                                  mcpDownloadRequest: {
                                    ...m.mcpDownloadRequest,
                                    downloadStatus: 'failed' as const,
                                    timeoutId: undefined
                                  },
                                  content: 'MCP技能下载已取消。'
                                };
                              }
                              return m;
                            }));

                            // 发送取消事件给后端
                            if (window.electron?.crossWindow) {
                              window.electron.crossWindow.emit('mcp:server-download-completed', {
                                success: false,
                                callbackId: msg.mcpDownloadRequest!.callbackId,
                                originalToolName: msg.mcpDownloadRequest!.originalToolName || msg.mcpDownloadRequest!.toolName
                              });
                            }
                          }}
                          className="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 text-sm font-medium rounded-md"
                        >
                          取消
                        </button>
                      </div>
                    )}
                    {/* 显示取消或失败消息 */}
                    {msg.mcpDownloadRequest.downloadStatus === 'failed' && (
                      <p className="text-sm text-blue-700 dark:text-blue-300 mb-2">
                        {msg.content || 'MCP技能下载已取消。'}
                      </p>
                    )}

                    {(!msg.mcpDownloadRequest.downloadStatus || msg.mcpDownloadRequest.downloadStatus === 'pending') && (
                      <p className="text-sm text-blue-700 dark:text-blue-300 mt-2">
                        需要下载并安装 "{getMcpServerName(msg.mcpDownloadRequest)}" MCP服务器
                      </p>
                    )}
                  </div>
                </div>
              )}

              {/* 只有在MCP下载完成或没有MCP下载请求时才显示内容 */}
              {(!msg.mcpDownloadRequest || msg.mcpDownloadRequest.downloadStatus === 'completed') && (
                <>
                  {/* Reasoning 内容区域 - 只有在深度思考开关启用且有reasoning内容时才显示 */}
              {currentReasoningContent && isDeepThinkingEnabled && (
                <div 
                  className="chat-item-reasoningcontent-container bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700" 
                  style={{
                    height: shouldShowReasoningExpanded ? 'auto' : 'fit-content',
                    maxHeight: shouldShowReasoningExpanded ? 'none' : '52px',
                    width: shouldShowReasoningExpanded ? '100%' : 'fit-content',
                    borderRadius: '8px',
                    marginBottom: '12px',
                    overflow: 'hidden',
                    cursor: isThinking ? 'default' : 'pointer' // thinking期间不显示指针
                  }}
                >
                  <div 
                    className="chat-item-reasoningcontent-title-container" 
                    style={{ 
                      padding: '16px', 
                      userSelect: 'none',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      width: '100%'
                    }}
                    onClick={(e) => {
                      // thinking期间禁用点击
                      if (isThinking) return;
                      e.preventDefault();
                      e.stopPropagation();
                      console.log('容器点击事件触发了, messageKey:', msg.key);
                      handleOpenReasoning(msg.key);
                    }}>
                    <div className="chat-item-reasoningcontent-title" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <Brain className="h-4 w-4" />
                      <span className="reasoning_content_title">
                        {isThinking ? '思考中...' : `已深度思考${msg.time ? '（用时' + msg.time + '秒）' : ''}`}
                      </span>
                    </div>
                    {/* thinking期间不显示切换图标 */}
                    {!isThinking && (
                      openReasoningMap[msg.key] ? <ChevronUp className="h-4 w-4"/> : <ChevronDown className="h-4 w-4" />
                    )}
                  </div>
                  {shouldShowReasoningExpanded && (
                    <div style={{ padding: '0 16px 16px 16px' }}>
                      <div className="markdown-content" dangerouslySetInnerHTML={{ 
                        __html: processFilePathsInHtml(md.render(currentReasoningContent))
                      }} />
                    </div>
                  )}
                </div>
              )}
              
              {/* 工具调用历史显示 */}
              {currentToolCalls.length > 0 && (
                <div className="tool-calls-history bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700" style={{ 
                  marginBottom: '16px',
                  padding: '12px 16px',
                  borderRadius: '8px',
                  transition: 'all 0.2s ease'
                }}>
                  <div style={{ 
                    fontSize: '13px', 
                    fontWeight: '600', 
                    color: '#475569',
                    marginBottom: '10px',
                    borderBottom: '1px solid #e2e8f0',
                    paddingBottom: '6px'
                  }}>
                    工具调用
                  </div>
                  {currentToolCalls.map((toolCall, index) => (
                    <div key={toolCall.id} style={{ 
                      marginBottom: index < currentToolCalls.length - 1 ? '10px' : '0',
                      padding: '6px 8px',
                      backgroundColor: toolCall.status === 'completed' ? 'rgba(16, 185, 129, 0.05)' : 'rgba(14, 165, 233, 0.05)',
                      borderRadius: '6px',
                      border: `1px solid ${toolCall.status === 'completed' ? 'rgba(16, 185, 129, 0.2)' : 'rgba(14, 165, 233, 0.2)'}`
                    }}>
                      <div 
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          cursor: 'pointer',
                          padding: '4px 0'
                        }}
                        onClick={() => {
                          handleToggleToolCall(msg.key, toolCall.id);
                        }}
                      >
                        <span style={{ 
                          fontSize: '14px',
                          color: toolCall.status === 'completed' ? '#059669' : '#0284c7',
                          fontWeight: '500',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px'
                        }}>
                          {toolCall.status === 'completed' ? (
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M20 6L9 17l-5-5"></path>
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <circle cx="12" cy="12" r="10"></circle>
                              <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                          )}
                          {toolCall.status === 'completed' ? 
                            `工具: ${toolCall.name} (已完成)` : 
                            `工具: ${toolCall.name} (处理中...)`}
                        </span>
                        {/* 始终显示展开/收起按钮 */}
                        <span style={{
                          fontSize: '12px',
                          color: '#64748b',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: '20px',
                          height: '20px',
                          borderRadius: '50%',
                          backgroundColor: toolCallsExpandedMap[msg.key]?.[toolCall.id] ? '#e2e8f0' : 'transparent',
                          transition: 'all 0.2s ease'
                        }}>
                          <svg 
                            xmlns="http://www.w3.org/2000/svg" 
                            width="14" 
                            height="14" 
                            viewBox="0 0 24 24" 
                            fill="none" 
                            stroke="currentColor" 
                            strokeWidth="2" 
                            strokeLinecap="round" 
                            strokeLinejoin="round"
                            style={{
                              transform: toolCallsExpandedMap[msg.key]?.[toolCall.id] ? 'rotate(180deg)' : 'rotate(0deg)',
                              transition: 'transform 0.2s'
                            }}
                          >
                            <polyline points="6 9 12 15 18 9"></polyline>
                          </svg>
                        </span>
                      </div>
                      
                      {/* 工具参数和结果展示 - 并排显示输入和输出 */}
                      {toolCallsExpandedMap[msg.key]?.[toolCall.id] && (
                        <div style={{
                          marginTop: '8px',
                          padding: '10px 12px',
                          backgroundColor: '#f1f5f9',
                          borderRadius: '6px',
                          border: '1px solid #e2e8f0',
                          transition: 'all 0.2s ease'
                        }}>
                          {/* 输入和输出并排布局 */}
                          <div style={{
                            display: 'grid',
                            gridTemplateColumns: toolCall.result ? '1fr 1fr' : '1fr',
                            gap: '12px',
                            width: '100%',
                            alignItems: 'stretch' // 确保两列高度一致
                          }}>
                            {/* 输入部分 */}
                            <div style={{ 
                              minWidth: 0,
                              display: 'flex',
                              flexDirection: 'column',
                              height: '100%'
                            }}>
                              <div style={{
                                fontSize: '12px',
                                color: '#475569',
                                marginBottom: '6px',
                                fontWeight: '600',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                gap: '4px',
                                flexShrink: 0 // 防止标题区域被压缩
                              }}>
                                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <path d="M8 3l4 8 5-5v11H4V6l4-3z"/>
                                  </svg>
                                  输入
                                </div>
                                {toolCall.args && (
                                  <div 
                                    title="复制输入"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (toolCall.args) {
                                        const copyKey = `${msg.key}-${toolCall.id}-input`;
                                        navigator.clipboard.writeText(JSON.stringify(toolCall.args, null, 2))
                                          .then(() => {
                                            setCopySuccessMap(prev => ({
                                              ...prev,
                                              [copyKey]: true
                                            }));
                                            
                                            setTimeout(() => {
                                              setCopySuccessMap(prev => {
                                                const newMap = { ...prev };
                                                delete newMap[copyKey];
                                                return newMap;
                                              });
                                            }, 3000);
                                          })
                                          .catch((err) => {
                                            console.error('复制失败:', err);
                                          });
                                      }
                                    }}
                                    style={{
                                      cursor: 'pointer',
                                      padding: '2px',
                                      borderRadius: '4px',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center'
                                    }}
                                  >
                                    {copySuccessMap[`${msg.key}-${toolCall.id}-input`] ? (
                                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="#10b981" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <path d="M20 6L9 17l-5-5"></path>
                                      </svg>
                                    ) : (
                                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                      </svg>
                                    )}
                                  </div>
                                )}
                              </div>
                              <div style={{ flex: 1, minHeight: 0 }}>
                                {toolCall.args ? (
                                  <pre style={{
                                    fontSize: '12px',
                                    color: '#334155',
                                    margin: 0,
                                    padding: '8px 10px',
                                    backgroundColor: '#ffffff',
                                    borderRadius: '4px',
                                    border: '1px solid #e2e8f0',
                                    whiteSpace: 'pre-wrap',
                                    wordBreak: 'break-word',
                                    height: toolCall.result ? '200px' : 'auto', // 当有输出时固定高度
                                    minHeight: toolCall.result ? '200px' : '80px', // 最小高度
                                    maxHeight: toolCall.result ? '200px' : '300px', // 最大高度
                                    overflowY: 'auto',
                                    boxSizing: 'border-box',
                                    // 自定义滚动条样式
                                    scrollbarWidth: 'thin', // Firefox
                                    scrollbarColor: '#cbd5e1 #f1f5f9', // Firefox
                                  }}
                                  className="custom-scrollbar">
                                    {JSON.stringify(toolCall.args, null, 2)}
                                  </pre>
                                ) : (
                                  <div style={{
                                    fontSize: '12px',
                                    color: '#64748b',
                                    padding: '8px 10px',
                                    backgroundColor: '#ffffff',
                                    borderRadius: '4px',
                                    border: '1px solid #e2e8f0',
                                    height: toolCall.result ? '200px' : 'auto',
                                    minHeight: toolCall.result ? '200px' : '80px',
                                    maxHeight: toolCall.result ? '200px' : '300px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    boxSizing: 'border-box',
                                    overflowY: 'auto',
                                    // 自定义滚动条样式
                                    scrollbarWidth: 'thin', // Firefox
                                    scrollbarColor: '#cbd5e1 #f1f5f9', // Firefox
                                  }}
                                  className="custom-scrollbar">
                                    此工具调用没有输入参数
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* 输出部分 - 只在有结果时显示 */}
                            {toolCall.result && (
                              <div style={{ 
                                minWidth: 0,
                                display: 'flex',
                                flexDirection: 'column',
                                height: '100%'
                              }}>
                                <div style={{
                                  fontSize: '12px',
                                  color: '#475569',
                                  marginBottom: '6px',
                                  fontWeight: '600',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'space-between',
                                  gap: '4px',
                                  flexShrink: 0 // 防止标题区域被压缩
                                }}>
                                  <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                      <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                      <circle cx="8.5" cy="7" r="4"/>
                                      <polyline points="17 11 19 13 23 9"/>
                                    </svg>
                                    输出
                                  </div>
                                  <div 
                                    title="复制输出"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (toolCall.result) {
                                        const copyKey = `${msg.key}-${toolCall.id}-output`;
                                        const resultText = typeof toolCall.result === 'string' 
                                          ? toolCall.result 
                                          : JSON.stringify(toolCall.result, null, 2);
                                        navigator.clipboard.writeText(resultText)
                                          .then(() => {
                                            setCopySuccessMap(prev => ({
                                              ...prev,
                                              [copyKey]: true
                                            }));
                                            
                                            setTimeout(() => {
                                              setCopySuccessMap(prev => {
                                                const newMap = { ...prev };
                                                delete newMap[copyKey];
                                                return newMap;
                                              });
                                            }, 3000);
                                          })
                                          .catch((err) => {
                                            console.error('复制失败:', err);
                                          });
                                      }
                                    }}
                                    style={{
                                      cursor: 'pointer',
                                      padding: '2px',
                                      borderRadius: '4px',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center'
                                    }}
                                  >
                                    {copySuccessMap[`${msg.key}-${toolCall.id}-output`] ? (
                                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="#10b981" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <path d="M20 6L9 17l-5-5"></path>
                                      </svg>
                                    ) : (
                                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                      </svg>
                                    )}
                                  </div>
                                </div>
                                <div style={{ flex: 1, minHeight: 0 }}>
                                  <pre style={{
                                    fontSize: '12px',
                                    color: '#334155',
                                    margin: 0,
                                    padding: '8px 10px',
                                    backgroundColor: '#ffffff',
                                    borderRadius: '4px',
                                    border: '1px solid #e2e8f0',
                                    whiteSpace: 'pre-wrap',
                                    wordBreak: 'break-word',
                                    height: '200px', // 固定高度与输入一致
                                    minHeight: '200px',
                                    maxHeight: '200px',
                                    overflowY: 'auto',
                                    boxSizing: 'border-box',
                                    // 自定义滚动条样式
                                    scrollbarWidth: 'thin', // Firefox
                                    scrollbarColor: '#cbd5e1 #f1f5f9', // Firefox
                                  }}
                                  className="custom-scrollbar">
                                    {typeof toolCall.result === 'string' 
                                      ? toolCall.result 
                                      : JSON.stringify(toolCall.result, null, 2)}
                                  </pre>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
              
              {/* 普通内容 */}
              <div className="markdown-content" dangerouslySetInnerHTML={{
                __html: processFilePathsInHtml(md.render(filteredCurrentContent))
              }} />
              {/* 音频播放器 - 只在聊天完成且有最终音频数据时显示 */}
              {msg.audio_data && msg.audio_data.length > 0 && !isCurrentStreaming && (
                <div>
                  {/* 合并所有音频段并显示播放器 */}
                  {(() => {
                    console.log('🎵 准备合并最终音频数据:', {
                      audioDataLength: msg.audio_data.length,
                      audioDataSizes: msg.audio_data.map((data, index) => ({
                        index,
                        size: data.byteLength
                      }))
                    });
                    const mergedData = mergeAudioBuffers(msg.audio_data);
                    console.log('🎵 最终合并结果:', {
                      mergedSize: mergedData.byteLength,
                      isArrayBuffer: mergedData instanceof ArrayBuffer
                    });

                    return <AudioPlayer audioData={mergedData} />;
                  })()}
                </div>
              )}
              </>
              )}
            </div>
          </Typography>
        );
      },
    }));
  }, [messageHistory, streamingText, loading, openReasoningMap, streamingReasoning, handleOpenReasoning, toolCallsExpandedMap, copySuccessMap, filterToolCallsFromText]);



  // 异步检查文件存在性并更新按钮状态
  const checkFileExistenceAndUpdateButtons = useCallback(async () => {
    // 查找所有未检查的文件路径容器
    const uncheckedContainers = document.querySelectorAll('.file-path-container[data-file-checked="false"]');
    
    for (const container of uncheckedContainers) {
      const htmlContainer = container as HTMLElement;
      const filePath = htmlContainer.getAttribute('data-file-path');
      const isDirectory = htmlContainer.getAttribute('data-is-directory') === 'true';
      
      if (!filePath) continue;
      
      try {
        // 检查文件/目录是否存在
        const pathExists = await window.electron.files.checkExists(filePath);
        
        // 标记为已检查
        htmlContainer.setAttribute('data-file-checked', 'true');
        
        if (pathExists) {
          // 文件/目录存在，激活按钮
          const openBtn = htmlContainer.querySelector('.file-open-btn') as HTMLElement;
          const locationBtn = htmlContainer.querySelector('.file-location-btn') as HTMLElement;
          
          if (openBtn) {
            openBtn.style.opacity = '1';
            openBtn.style.cursor = 'pointer';
          }
          if (locationBtn) {
            locationBtn.style.opacity = '1';
            locationBtn.style.cursor = 'pointer';
          }
        } else {
          // 文件/目录不存在，移除整个容器，恢复原始文本
          const originalText = htmlContainer.getAttribute('data-file-path') || '';
          const textNode = document.createTextNode(originalText);
          htmlContainer.parentNode?.replaceChild(textNode, htmlContainer);
        }
      } catch (error) {
        console.warn(`检查${isDirectory ? '目录' : '文件'}存在性失败: ${filePath}`, error);
        // 出错时移除容器，恢复原始文本
        const originalText = htmlContainer.getAttribute('data-file-path') || '';
        const textNode = document.createTextNode(originalText);
        htmlContainer.parentNode?.replaceChild(textNode, htmlContainer);
      }
    }
  }, []);


  // 用于跟踪已处理的query，避免重复处理
  const processedQueryRef = useRef<string>('');
  // 用于跟踪query的变化次数
  const queryChangeCountRef = useRef<number>(0);

  // 当query参数变化时自动启动对话
  useEffect(() => {
    if (query && query.trim() && !loading) {
      // 提取真正的查询内容（去掉时间戳标识符）
      const actualQuery = query.includes('#') ? query.split('#')[0] : query;

      // 检查query是否真的发生了变化
      if (query !== processedQueryRef.current) {
        console.log('自动启动AI对话，query:', query, '实际查询:', actualQuery, '上次处理的query:', processedQueryRef.current);
        processedQueryRef.current = query;
        queryChangeCountRef.current += 1;
        // 使用实际的查询内容启动对话
        startConversation(actualQuery);
      } else {
        console.log('🚫 忽略重复的query:', query);
      }
    }
  }, [query, loading, startConversation]);


  // 检查文件存在性和消息历史变化时的副作用
  useEffect(() => {
    // 检查文件存在性并更新按钮状态
    const timeoutId = setTimeout(() => {
      checkFileExistenceAndUpdateButtons();
    }, 100); // 延迟一点确保DOM已渲染

    return () => clearTimeout(timeoutId);
  }, [messageHistory, checkFileExistenceAndUpdateButtons]);

  // 组件卸载时清理WebSocket连接
  useEffect(() => {
    return () => {
      // 在开发环境下，React严格模式会重复挂载组件，不要立即清理连接
      if (process.env.NODE_ENV === 'development') {
        console.log('🔌 开发环境下不清理WebSocket连接，避免React严格模式问题');
        return;
      }

      // 生产环境下正常清理
      import('../../../../llm/services/WebSocketChatService').then(({ default: WebSocketChatService }) => {
        WebSocketChatService.cleanup();
        console.log('🔌 AI聊天组件卸载时已完全断开WebSocket连接');
      }).catch(error => {
        console.error('组件卸载时清理WebSocket连接失败:', error);
      });
    };
  }, []);

  // 处理MCP工具调用开始事件
  const handleMcpToolCallStart = useCallback((data: {
    toolName: string;
    toolArgs: any;
    serverName: string;
    status: string;
    timestamp: number;
  }) => {
    console.log('收到MCP工具调用开始事件:', data);
    
    // 检查当前是否有活跃的AI消息
    const currentAiMessage = messageHistory.find(msg => 
      msg.key === currentMessageId.current && msg.role === 'assistant'
    );
    
    if (currentAiMessage) {
      // 更新当前AI消息的工具调用
      setMessageHistory(prev => 
        prev.map(msg => {
          if (msg.key === currentMessageId.current && msg.role === 'assistant') {
            const existingToolCalls = msg.tool_calls || [];
            const newToolCall: ToolCall = {
              id: `${data.toolName}-${data.timestamp}`,
              name: data.toolName,
              displayName: `正在使用工具: ${data.serverName}`,
              args: data.toolArgs,
              status: 'calling' as const,
              expanded: false
            };
            
            // 检查是否已存在相同工具名称的调用
            const existingIndex = existingToolCalls.findIndex(tool => 
              tool.name === data.toolName && tool.status === 'calling'
            );
            
            if (existingIndex >= 0) {
              // 更新现有的工具调用
              const updatedToolCalls = [...existingToolCalls];
              updatedToolCalls[existingIndex] = {
                ...updatedToolCalls[existingIndex],
                args: data.toolArgs,
                status: 'calling' as const
              };
              return { ...msg, tool_calls: updatedToolCalls };
            } else {
              // 添加新的工具调用
              return { 
                ...msg, 
                tool_calls: [...existingToolCalls, newToolCall]
              };
            }
          }
          return msg;
        })
      );
    } else {
      // 如果没有活跃的AI消息，创建一个新的
      const newAiMessage: ChatMessage = {
        role: 'assistant',
        content: '',
        key: `ai-${Date.now()}`,
        timestamp: Date.now(),
        tool_calls: [{
          id: `${data.toolName}-${data.timestamp}`,
          name: data.toolName,
          displayName: `正在使用工具: ${data.serverName}`,
          args: data.toolArgs,
          status: 'calling' as const,
          expanded: false
        }]
      };
      
      setMessageHistory(prev => [...prev, newAiMessage]);
      currentMessageId.current = newAiMessage.key;
    }
  }, [messageHistory]);

  // 处理MCP工具调用完成事件
  const handleMcpToolCallComplete = useCallback((data: {
    toolName: string;
    toolArgs: any;
    serverName: string;
    result: any;
    status: string;
    timestamp: number;
  }) => {
    console.log('收到MCP工具调用完成事件:', data);
    
    setMessageHistory(prev => 
      prev.map(msg => {
        if (msg.tool_calls) {
          const updatedToolCalls = msg.tool_calls.map(toolCall => {
            if (toolCall.name === data.toolName && toolCall.status === 'calling') {
              return {
                ...toolCall,
                result: data.result,
                status: 'completed' as const,
                displayName: toolCall.displayName.replace('正在使用工具:', '已完成工具:')
              };
            }
            return toolCall;
          });
          
          if (updatedToolCalls.some(tool => tool.name === data.toolName)) {
            return { ...msg, tool_calls: updatedToolCalls };
          }
        }
        return msg;
      })
    );
  }, []);

  // 处理MCP工具调用错误事件
  const handleMcpToolCallError = useCallback((data: {
    toolName: string;
    toolArgs: any;
    serverName: string;
    error: string;
    status: string;
    timestamp: number;
  }) => {
    console.log('收到MCP工具调用错误事件:', data);
    
    setMessageHistory(prev => 
      prev.map(msg => {
        if (msg.tool_calls) {
          const updatedToolCalls = msg.tool_calls.map(toolCall => {
            if (toolCall.name === data.toolName && toolCall.status === 'calling') {
              return {
                ...toolCall,
                result: { error: data.error },
                status: 'completed' as const,
                displayName: toolCall.displayName.replace('正在使用工具:', '工具调用失败:')
              };
            }
            return toolCall;
          });
          
          if (updatedToolCalls.some(tool => tool.name === data.toolName)) {
            return { ...msg, tool_calls: updatedToolCalls };
          }
        }
        return msg;
      })
    );
  }, []);

  // 监听MCP工具调用事件
  useEffect(() => {
    // 监听工具调用开始事件
    const handleToolCallStart = (event: CustomEvent) => {
      handleMcpToolCallStart(event.detail);
    };

    // 监听工具调用完成事件
    const handleToolCallComplete = (event: CustomEvent) => {
      handleMcpToolCallComplete(event.detail);
    };

    // 监听工具调用错误事件
    const handleToolCallError = (event: CustomEvent) => {
      handleMcpToolCallError(event.detail);
    };

    // 添加事件监听器 - 使用跨窗口通信API
    if (window.electron?.crossWindow) {
      // 监听来自主进程的MCP工具调用事件
      const unsubscribeStart = window.electron.crossWindow.on('ai-chat:tool-call-start', (data: any) => {
        console.log('收到来自主进程的工具调用开始事件:', data);
        handleMcpToolCallStart(data);
      });

      const unsubscribeComplete = window.electron.crossWindow.on('ai-chat:tool-call-complete', (data: any) => {
        console.log('收到来自主进程的工具调用完成事件:', data);
        handleMcpToolCallComplete(data);
      });

      const unsubscribeError = window.electron.crossWindow.on('ai-chat:tool-call-error', (data: any) => {
        console.log('收到来自主进程的工具调用错误事件:', data);
        handleMcpToolCallError(data);
      });

      // 同时保留DOM事件监听器作为备用
      window.addEventListener('ai-chat:tool-call-start', handleToolCallStart as EventListener);
      window.addEventListener('ai-chat:tool-call-complete', handleToolCallComplete as EventListener);
      window.addEventListener('ai-chat:tool-call-error', handleToolCallError as EventListener);

      // 清理函数
      return () => {
        // 移除跨窗口事件监听器
        unsubscribeStart();
        unsubscribeComplete();
        unsubscribeError();

        // 移除DOM事件监听器
        window.removeEventListener('ai-chat:tool-call-start', handleToolCallStart as EventListener);
        window.removeEventListener('ai-chat:tool-call-complete', handleToolCallComplete as EventListener);
        window.removeEventListener('ai-chat:tool-call-error', handleToolCallError as EventListener);
      };
    } else {
      // 如果没有跨窗口通信API，只使用DOM事件
      window.addEventListener('ai-chat:tool-call-start', handleToolCallStart as EventListener);
      window.addEventListener('ai-chat:tool-call-complete', handleToolCallComplete as EventListener);
      window.addEventListener('ai-chat:tool-call-error', handleToolCallError as EventListener);

      // 清理函数
      return () => {
        window.removeEventListener('ai-chat:tool-call-start', handleToolCallStart as EventListener);
        window.removeEventListener('ai-chat:tool-call-complete', handleToolCallComplete as EventListener);
        window.removeEventListener('ai-chat:tool-call-error', handleToolCallError as EventListener);
      };
    }
  }, [handleMcpToolCallStart, handleMcpToolCallComplete, handleMcpToolCallError]);

  return (
    <div
      ref={chatContainerRef}
      className="bg-background dark:bg-gray-800 dark:text-gray-100 rounded-b-lg overflow-y-auto max-h-[85vh] p-4 custom-scrollbar"
      onMouseLeave={() => {
        // 当鼠标离开整个聊天容器时，清理所有tooltip作为额外保护
        const tooltips = document.querySelectorAll('.custom-tooltip');
        tooltips.forEach(tooltip => {
          const htmlTooltip = tooltip as HTMLElement;
          htmlTooltip.style.opacity = '0';
          setTimeout(() => {
            if (htmlTooltip.parentNode) {
              htmlTooltip.parentNode.removeChild(htmlTooltip);
            }
          }, 200);
        });
      }}
    >
      <Bubble.List items={messages as BubbleProps<string>[]} />
    </div>
  );
}); 
export default AiChat;