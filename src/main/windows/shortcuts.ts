import { globalShortcut, BrowserWindow } from 'electron';
import { settingsService } from '../services/settingsService';
import { IWindowManagerStatic } from '../types/window';
import * as log from '../logging';
/**
 * 注册全局快捷键
 * @param windowManager 窗口管理器对象，用于操作窗口
 */
export function registerGlobalShortcuts(windowManager: IWindowManagerStatic): void {
  try {
    // 只注销系统快捷键，不影响应用快捷键
    const settings = settingsService.getSettings();
    
    // 注销主窗口快捷键
    if (settings.general.hotkey && globalShortcut.isRegistered(settings.general.hotkey)) {
      globalShortcut.unregister(settings.general.hotkey);
    }
    
    // 注销剪贴板快捷键
    if (settings.clipboard.hotkey && globalShortcut.isRegistered(settings.clipboard.hotkey)) {
      globalShortcut.unregister(settings.clipboard.hotkey);
    }

    // 添加延迟确保注销完成
    setTimeout(() => {
      registerMainWindowShortcut(windowManager);
      registerClipboardShortcut(windowManager);
    }, 100);

    log.debug('系统快捷键注册完成');
  } catch (error) {
    log.error('注册系统快捷键失败:', error);
  }
}

/**
 * 注册主窗口快捷键
 */
export function registerMainWindowShortcut(windowManager: IWindowManagerStatic): void {
  const mainHotkey = settingsService.getSettings().general.hotkey;
  log.debug('正在注册主窗口快捷键:', mainHotkey);

  // 检查是否已被占用
  if (globalShortcut.isRegistered(mainHotkey)) {
    log.warn('主窗口快捷键已被注册，尝试注销后重新注册:', mainHotkey);
    globalShortcut.unregister(mainHotkey);
  }

  const registered = globalShortcut.register(mainHotkey, () => {
    log.debug('触发主窗口快捷键:', mainHotkey);
    windowManager.toggleMainWindow();
  });

  if (registered) {
    log.debug('成功注册主窗口快捷键:', mainHotkey);
  } else {
    log.error('主窗口快捷键注册失败:', mainHotkey);
  }
}

/**
 * 为特定窗口注册本地快捷键
 * @param window 浏览器窗口实例
 * @param windowManager 窗口管理器
 */
export function registerLocalShortcuts(window: BrowserWindow, windowManager: IWindowManagerStatic): void {
  // 注册ESC键为窗口级别快捷键
  window.webContents.on('before-input-event', (event, input) => {
    if (input.key === 'Escape' && !input.control && !input.alt && !input.meta && !input.shift) {
      log.debug('捕获到ESC键');
      windowManager.hideMainWindow();
      // 阻止事件继续传播
      event.preventDefault();
    }
  });
}

/**
 * 注册剪贴板快捷键
 */
export function registerClipboardShortcut(windowManager: IWindowManagerStatic): void {
  const settings = settingsService.getSettings();
  const clipboardHotkey = settings.clipboard.hotkey;
  
  if (!clipboardHotkey) {
    log.warn('剪贴板快捷键为空，跳过注册');
    return;
  }

  log.debug('正在注册剪贴板快捷键:', clipboardHotkey);

  // 检查是否已被占用
  if (globalShortcut.isRegistered(clipboardHotkey)) {
    log.warn('剪贴板快捷键已被注册，尝试注销后重新注册:', clipboardHotkey);
    globalShortcut.unregister(clipboardHotkey);
  }

  try {
    const registered = globalShortcut.register(clipboardHotkey, async () => {
      log.debug('触发剪贴板历史快捷键:', clipboardHotkey);
      try {
        await windowManager.toggleClipboardHistory();
      } catch (error) {
        log.error('显示剪贴板历史窗口失败:', error);
      }
    });

    if (registered) {
      log.debug('成功注册剪贴板快捷键:', clipboardHotkey);
    } else {
      log.error('剪贴板快捷键注册失败:', clipboardHotkey);
    }
  } catch (error) {
    log.error('剪贴板快捷键注册异常:', error);
  }
}

/**
 * 重新注册剪贴板快捷键 - 用于配置更新后
 * 确保只有一个剪贴板快捷键存在
 */
export function reregisterClipboardShortcut(windowManager: IWindowManagerStatic): void {
  const settings = settingsService.getSettings();
  log.debug('重新注册剪贴板快捷键:', settings.clipboard.hotkey);

  // 只注销系统快捷键，不影响应用快捷键
  if (settings.general.hotkey && globalShortcut.isRegistered(settings.general.hotkey)) {
    globalShortcut.unregister(settings.general.hotkey);
  }
  
  if (settings.clipboard.hotkey && globalShortcut.isRegistered(settings.clipboard.hotkey)) {
    globalShortcut.unregister(settings.clipboard.hotkey);
  }

  // 延迟后重新注册系统快捷键
  setTimeout(() => {
    registerMainWindowShortcut(windowManager);
    registerClipboardShortcut(windowManager);
  }, 300); // 增加延迟确保注销完成
}

/**
 * 注销所有全局快捷键
 */
export function unregisterAllShortcuts(): void {
  log.debug('注销所有全局快捷键');
  globalShortcut.unregisterAll();
} 